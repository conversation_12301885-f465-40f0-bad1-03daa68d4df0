import type { CodegenConfig } from '@graphql-codegen/cli';
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

const config: CodegenConfig = {
  overwrite: true,
  schema: process.env.REACT_APP_API_URL,
  documents: [
    // "**/*.tsx",
    // "**/*.ts",
    '**/*.graphql',
    '!**/node_modules',
    // "!graphql",
    '!generated',
  ],
  generates: {
    'src/generated/graphql.ts': {
      // preset: "client",
      plugins: ['typescript', 'typescript-operations', 'typescript-react-apollo'],
    },
  },
  ignoreNoDocuments: true,
};

export default config;
