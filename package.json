{"name": "unity-frontend", "version": "0.1.0", "private": true, "engines": {"node": "^20", "npm": "^10"}, "dependencies": {"@apollo/client": "^3.11.8", "@craco/craco": "^7.1.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-table": "^8.20.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.107", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "eslint-import-resolver-alias": "^1.1.2", "graphql": "^16.9.0", "husky": "^9.1.5", "input-otp": "^1.2.4", "lucide-react": "^0.438.0", "next-themes": "^0.3.0", "nuqs": "^2.4.1", "pluralize": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-paginate": "^8.3.0", "react-router-dom": "^6.26.1", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "web-vitals": "^2.1.4", "zod": "^3.23.8"}, "lint-staged": {"*.{js,jsx,ts,tsx,md,html,css}": "prettier --write"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint src", "prepare": "husky", "codegen": "graphql-codegen --config codegen.ts"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@eslint/js": "^9.9.1", "@graphql-codegen/cli": "5.0.3", "@graphql-codegen/client-preset": "4.5.0", "@graphql-codegen/typescript-operations": "^4.3.1", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@types/pluralize": "^0.0.33", "@types/react": "^18.3.12", "@types/react-helmet": "^6.1.11", "@types/react-window-infinite-loader": "^1.0.9", "autoprefixer": "^10.4.20", "dotenv": "^16.4.7", "eslint": "^8.57.0", "eslint-plugin-react": "^7.35.2", "eslint-plugin-react-hooks": "^4.6.2", "globals": "^15.9.0", "lint-staged": "^15.2.10", "postcss": "^8.4.45", "prettier": "3.3.3", "tailwindcss": "^3.4.10", "typescript": "^4.9.5", "typescript-eslint": "^8.4.0"}}