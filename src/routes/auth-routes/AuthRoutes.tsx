import { RouteObject } from 'react-router-dom';
import { SignInPage } from '@/pages/auth/sign-in/Loadable';
import ProtectedRoutes from '../ProtectedRoutes';
import AuthLayout from '@/layouts/AuthLayout';
import { AppRoutePaths } from '@/lib/constants';
import { ForgotPasswordPage } from '@/pages/auth/forgot-password/Loadable';
import { ResetPasswordPage } from '@/pages/auth/reset-password/Loadable';
import { CompleteSignUpPage } from '@/pages/auth/complete-sign-up/Loadable';

export const AuthRoutes: RouteObject[] = [
  {
    path: '/auth',
    element: (
      <ProtectedRoutes
        excludeRoutes={[
          AppRoutePaths.SIGN_IN,
          AppRoutePaths.FORGOT_PASSWORD,
          AppRoutePaths.RESET_PASSWORD,
          AppRoutePaths.COMPLETE_SIGN_UP,
        ]}
      >
        <AuthLayout />
      </ProtectedRoutes>
    ),
    children: [
      { path: 'sign-in', element: <SignInPage /> },
      {
        path: 'forgot-password',
        element: <ForgotPasswordPage />,
      },
      {
        path: 'reset-password',
        element: <ResetPasswordPage />,
      },
      {
        path: 'complete-sign-up',
        element: <CompleteSignUpPage />,
      },
    ],
  },
];
