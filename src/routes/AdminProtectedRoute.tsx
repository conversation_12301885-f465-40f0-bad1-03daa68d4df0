import LoadingOverlay from '@/components/loading/LoadingOverlay';
import { UserRole } from '@/generated/graphql';
import { AppRoutePaths } from '@/lib/constants';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { Navigate } from 'react-router-dom';

const AdminProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { userData, isLoading, isAuthenticated } = useAuthContext();

  // Wait for authentication and user data to be loaded
  if (isLoading || (!userData && isAuthenticated)) {
    return <LoadingOverlay isLoading className='w-8 h-8' />;
  }

  // If user is not authenticated or doesn't have admin role
  if (!isAuthenticated || userData?.role !== UserRole.Admin) {
    return <Navigate to={AppRoutePaths.USERS} replace />;
  }

  return <>{children}</>;
};

export default AdminProtectedRoute;
