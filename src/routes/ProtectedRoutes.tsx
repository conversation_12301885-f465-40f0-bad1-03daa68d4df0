import LoadingOverlay from '@/components/loading/LoadingOverlay';
import { AppRoutePaths } from '@/lib/constants';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { Navigate, useLocation } from 'react-router-dom';

interface Props {
  children: React.ReactNode;
  excludeRoutes?: string[];
}

const ProtectedRoutes = ({ children, excludeRoutes = [] }: Props) => {
  const location = useLocation();
  const path = location.pathname;

  const { isAuthenticated, isLoading } = useAuthContext();

  if (isLoading) {
    return <LoadingOverlay isLoading className='w-8 h-8' />;
  }

  if (isAuthenticated && excludeRoutes.includes(path)) {
    return <Navigate to={AppRoutePaths.USERS} replace />;
  }

  if (!isAuthenticated && !excludeRoutes.includes(path)) {
    return <Navigate to={AppRoutePaths.SIGN_IN} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoutes;
