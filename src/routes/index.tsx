import { createBrowserRouter, Navigate } from 'react-router-dom';
import NotFoundPage from '../pages/not-found';
import { AuthRoutes } from './auth-routes/AuthRoutes';
import ProtectedRoutes from './ProtectedRoutes';
import { UsersPage } from '@/pages/users/Loadable';
import { SettingsPage } from '@/pages/settings/Loadable';
import Layout from '@/layouts/Layout';
import { AppRoutePaths } from '@/lib/constants';
import { EditUserPage } from '@/pages/edit-user/Loadable';
import AdminProtectedRoute from './AdminProtectedRoute';
import { UnsubscribeEmailPage } from '@/pages/unsubscribe-email/Loadable';
import { ClubsPage } from '@/pages/clubs/Loadable';
import { ClubTemplateFormPage } from '@/pages/clubs/template-form/Loadable';
import { AssociationsPage } from '@/pages/associations/Loadable';
import { ClubDetailPage } from '@/pages/club-detail/Loadable';
import { AssociationDetailPage } from '@/pages/association-detail/Loadable';

export const router = createBrowserRouter([
  ...AuthRoutes,
  {
    path: '/',
    element: (
      <ProtectedRoutes>
        <Layout />
      </ProtectedRoutes>
    ),
    children: [
      { index: true, element: <Navigate to={AppRoutePaths.USERS} replace /> },
      { path: AppRoutePaths.USERS, element: <UsersPage /> },
      {
        path: `${AppRoutePaths.USERS}/:id`,
        element: (
          <AdminProtectedRoute>
            <EditUserPage />
          </AdminProtectedRoute>
        ),
      },
      { path: AppRoutePaths.ASSOCIATIONS, element: <AssociationsPage /> },
      { path: `${AppRoutePaths.ASSOCIATIONS}/:id`, element: <AssociationDetailPage /> },
      {
        path: `${AppRoutePaths.ASSOCIATIONS}/:associationId/clubs/:clubId`,
        element: <ClubDetailPage />,
      },
      { path: AppRoutePaths.SETTINGS, element: <SettingsPage /> },
      { path: AppRoutePaths.CLUBS, element: <ClubsPage /> },
      {
        path: `${AppRoutePaths.CLUBS}/create`,
        element: (
          <AdminProtectedRoute>
            <ClubTemplateFormPage />
          </AdminProtectedRoute>
        ),
      },
      {
        path: `${AppRoutePaths.CLUBS}/:id`,
        element: (
          <AdminProtectedRoute>
            <ClubTemplateFormPage />
          </AdminProtectedRoute>
        ),
      },
    ],
    errorElement: <NotFoundPage />,
  },
  { path: AppRoutePaths.UNSUBSCRIBE_EMAIL, element: <UnsubscribeEmailPage /> },
  { path: '*', element: <NotFoundPage /> },
]);
