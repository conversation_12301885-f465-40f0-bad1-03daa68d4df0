import Header from '@/components/header/Header';
import FormConfirmModal from '@/components/modals/form-modal/FormConfirmModal';
import { MultiFormGuardProvider, useMultiFormGuardContext } from '@/hooks/useMultiFormGuard';
import { useState } from 'react';
import { Outlet } from 'react-router-dom';
import RouteTitleManager from './RouterTitle';

const LayoutInner = () => {
  const { isModalOpen, onSaveAll, onDiscardAll } = useMultiFormGuardContext();
  const [isSaving, setIsSaving] = useState(false);
  const handleConfirm = async () => {
    setIsSaving(true);
    await onSaveAll();
    setIsSaving(false);
  };
  return (
    <>
      <Header />
      <main className='w-full flex-1 flex flex-col bg-background'>
        <Outlet />
      </main>

      <FormConfirmModal
        isOpen={isModalOpen}
        title='Unsaved changes'
        description='Do you want to save or discard changes?'
        cancelText='Discard'
        confirmText='Save changes'
        isHideClose
        onCancel={onDiscardAll}
        onConfirm={handleConfirm}
        isLoading={isSaving}
        width={400}
      />
    </>
  );
};

const Layout = () => {
  return (
    <MultiFormGuardProvider>
      <div className='flex flex-col w-full min-h-[100dvh] max-w-screen-2xl mx-auto'>
        <RouteTitleManager />
        <LayoutInner />
      </div>
    </MultiFormGuardProvider>
  );
};

export default Layout;
