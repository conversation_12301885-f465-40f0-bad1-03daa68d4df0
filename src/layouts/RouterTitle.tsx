import { AppRoutePaths } from '@/lib/constants';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';

/**
 * Map of route paths to their corresponding page titles
 * Used to set the browser tab title based on current route
 */
const routeTitles: { [key: string]: string } = {
  [AppRoutePaths.USERS]: 'Users',
  [AppRoutePaths.SETTINGS]: 'Settings',
  [AppRoutePaths.SIGN_IN]: 'Sign In',
  [AppRoutePaths.FORGOT_PASSWORD]: 'Forgot Password',
  [AppRoutePaths.RESET_PASSWORD]: 'Reset Password',
  [AppRoutePaths.COMPLETE_SIGN_UP]: 'Complete Sign Up',
  [AppRoutePaths.CLUBS]: 'Clubs',
  [AppRoutePaths.ASSOCIATIONS]: 'Associations',
  '/': 'Home',
};

/**
 * Default title used when route is not found in routeTitles
 */
const defaultTitle = 'Unity Admin';

/**
 * RouteTitleManager Component
 * Dynamically sets the document title based on current route
 */
const RouteTitleManager = () => {
  const { pathname } = useLocation();

  const routeTitle = routeTitles[pathname];
  const title = routeTitle ? `${routeTitle} - ${defaultTitle}` : defaultTitle;

  return (
    <Helmet>
      <title>{title}</title>
    </Helmet>
  );
};

export default RouteTitleManager;
