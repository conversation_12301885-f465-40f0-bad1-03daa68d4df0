import { SortingState } from '@tanstack/react-table';
import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export default function useSort() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [sorting, setSorting] = useState<SortingState>([]);
  const isInitializedRef = useRef(false);

  // Initialize sorting from URL on mount
  useEffect(() => {
    if (isInitializedRef.current) return;

    const sortField = searchParams.get('sortField');
    const sortDir = searchParams.get('sortDir');

    if (sortField) {
      setSorting([
        {
          id: sortField,
          desc: sortDir === 'desc',
        },
      ]);
    }

    isInitializedRef.current = true;
  }, [searchParams]);

  // Update URL when sorting changes (but not during initialization)
  useEffect(() => {
    if (!isInitializedRef.current) return;

    const newParams = new URLSearchParams(searchParams);

    if (sorting.length > 0) {
      newParams.set('sortField', sorting[0].id);
      newParams.set('sortDir', sorting[0].desc ? 'desc' : 'asc');
    } else {
      newParams.delete('sortField');
      newParams.delete('sortDir');
    }

    // Only update if the params actually changed
    const currentParams = searchParams.toString();
    const newParamsString = newParams.toString();

    if (currentParams !== newParamsString) {
      setSearchParams(newParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sorting, searchParams]);

  return { sorting, setSorting };
}
