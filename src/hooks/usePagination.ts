import { PaginationState, Updater } from '@tanstack/react-table';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export default function usePagination(initPageIdx: number = 1) {
  const [params, setParams] = useSearchParams();
  const [pagination, setPagination] = useState<PaginationState>(() => {
    const pageParam = params.get('page');

    return {
      pageIndex: pageParam ? parseInt(pageParam, 10) : initPageIdx,
      pageSize: 10,
    };
  });

  const firstRender = useRef(true);

  useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false;
      return;
    }

    const pageParam = params.get('page');

    setPagination((prev) => ({
      ...prev,
      pageIndex: pageParam ? parseInt(pageParam, 10) || 1 : prev.pageIndex,
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (firstRender.current) return;

    const currentPageParam = params.get('page');
    const nextPageParam = pagination.pageIndex.toString();

    // Avoid unnecessary history entries if value is unchanged
    if (currentPageParam === nextPageParam) return;

    const newParams = new URLSearchParams(params);
    newParams.set('page', nextPageParam);

    // Use replace on first-time init (no existing page param) to avoid trapping back navigation
    setParams(newParams, { replace: currentPageParam === null });
  }, [pagination.pageIndex, params, setParams]);

  const updatePagination = useCallback(
    (newPagination: PaginationState | Updater<PaginationState>) => {
      setPagination((prev) =>
        typeof newPagination === 'function' ? newPagination(prev) : newPagination
      );
    },
    []
  );

  return {
    pagination,
    setPagination: updatePagination,
  };
}
