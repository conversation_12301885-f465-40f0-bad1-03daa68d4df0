import { useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDebounce } from './useDebounce';

export const useSearchQuery = (tabId?: string) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const searchKey = tabId ? `search_${tabId}` : 'search';
  const [searchTemp, setSearchTemp] = useState(searchParams.get(searchKey) || '');

  const searchDebounce = useDebounce(searchTemp, 500);

  const handleSearchChange = useCallback(() => {
    const newParams = new URLSearchParams(Array.from(searchParams.entries()));
    const currentSearch = searchParams.get(searchKey) || '';

    // Only update if the debounced search value has actually changed
    if (searchDebounce !== currentSearch) {
      if (searchDebounce.length === 0) {
        newParams.delete(searchKey);
      } else {
        newParams.set(searchKey, searchDebounce);
      }

      // Reset to page 1 only when search actually changes (not during debounce)
      newParams.set('page', '1');
      setSearchParams(newParams);
    }
  }, [searchDebounce, searchParams, setSearchParams, searchKey]);

  useEffect(() => {
    handleSearchChange();
  }, [handleSearchChange]);

  return { searchTemp: searchDebounce, setSearchTemp };
};
