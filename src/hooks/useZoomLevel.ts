import { ZOOM_LEVEL_THRESHOLD } from '@/lib/constants';
import { useState, useEffect } from 'react';

const BASE_PIXEL_RATIO_KEY = 'basePixelRatio';

export const useZoomLevel = () => {
  const [zoomLevel, setZoomLevel] = useState(100);
  const [basePixelRatio, setBasePixelRatio] = useState(() => {
    const stored = localStorage.getItem(BASE_PIXEL_RATIO_KEY);
    return stored ? parseFloat(stored) : window.devicePixelRatio;
  });

  useEffect(() => {
    // Only store the initial devicePixelRatio if it hasn't been stored before
    if (!localStorage.getItem(BASE_PIXEL_RATIO_KEY)) {
      localStorage.setItem(BASE_PIXEL_RATIO_KEY, window.devicePixelRatio.toString());
      setBasePixelRatio(window.devicePixelRatio);
    }

    const updateZoomLevel = () => {
      const currentRatio = window.devicePixelRatio;
      const relativeZoom = (currentRatio / basePixelRatio) * 100;
      const level = Math.round(relativeZoom);
      setZoomLevel(level);
    };

    // Initial check
    updateZoomLevel();

    // Listen for zoom changes
    window.addEventListener('resize', updateZoomLevel);
    // Also listen for devicePixelRatio changes
    window.matchMedia('screen and (min-resolution: 2dppx)').addListener(updateZoomLevel);

    return () => {
      window.removeEventListener('resize', updateZoomLevel);
    };
  }, [basePixelRatio]);

  return zoomLevel;
};

export const useHideLogoOnZoom = () => {
  const zoomLevel = useZoomLevel();
  return zoomLevel > ZOOM_LEVEL_THRESHOLD;
};
