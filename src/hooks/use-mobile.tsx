import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';
import { useEffect, useState } from 'react';

export const useMediaQuery = (maxWidth: number, minWidth = 0) => {
  const [targetReached, setTargetReached] = useState(() => {
    if (typeof window !== 'undefined') {
      const media = window.matchMedia(`(min-width: ${minWidth}px) and (max-width: ${maxWidth}px)`);
      return media.matches;
    }
    return false;
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const media = window.matchMedia(`(min-width: ${minWidth}px) and (max-width: ${maxWidth}px)`);
      setTargetReached(media.matches);

      const updateTarget = (e: MediaQueryListEvent) => {
        setTargetReached(e.matches);
      };

      media.addEventListener('change', updateTarget);
      return () => media.removeEventListener('change', updateTarget);
    }
  }, [maxWidth, minWidth]);

  return targetReached;
};

export const useIsMobile = () => {
  return useMediaQuery(MOBILE_DEVICE_WIDTH);
};
