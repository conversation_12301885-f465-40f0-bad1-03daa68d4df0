import { useCallback, useRef } from 'react';

interface UseIntersectionObserverProps {
  hasNextPage?: boolean;
  fetchNextPage?: () => void;
  threshold?: number;
  isFetchingNextPage?: boolean;
}

export function useIntersectionObserver({
  hasNextPage,
  fetchNextPage,
  threshold = 0.1,
  isFetchingNextPage,
}: UseIntersectionObserverProps) {
  const observer = useRef<IntersectionObserver | null>(null);

  const lastEntryRef = useCallback(
    (node: Element | null) => {
      if (isFetchingNextPage) return;

      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            fetchNextPage?.();
          }
        },
        { threshold }
      );

      if (node) observer.current.observe(node);
    },
    [isFetchingNextPage, threshold, hasNextPage, fetchNextPage]
  );

  return lastEntryRef;
}
