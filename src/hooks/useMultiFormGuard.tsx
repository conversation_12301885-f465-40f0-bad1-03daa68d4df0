import { createContext, useContext, useRef, useState } from 'react';

type FormEntry = {
  isDirty: boolean;
  save: () => Promise<void>;
};

type FormMap = Record<string, FormEntry>;

type MultiFormGuardContextType = {
  registerForm: (formId: string, save: () => Promise<void>, isDirty: boolean) => void;
  unregisterForm: (formId: string) => void;
  setFormDirty: (formId: string, isDirty: boolean) => void;
  getDirtyForms: () => { formId: string; save: () => Promise<void> }[];
  requestTransition: (nextAction: () => void) => void;
  onSaveAll: () => Promise<void>;
  onDiscardAll: () => void;
  isModalOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
};

const MultiFormGuardContext = createContext<MultiFormGuardContextType | null>(null);

/**
 * Provides internal logic to track multiple forms and manage unsaved changes modal
 */
export const useMultiFormGuardProvider = (): MultiFormGuardContextType => {
  const formMapRef = useRef<FormMap>({}); // Track all registered forms
  const [isModalOpen, setIsModalOpen] = useState(false);
  const nextActionRef = useRef<() => void>(() => {}); // Action to run after confirmation

  const registerForm = (formId: string, save: () => Promise<void>, isDirty: boolean) => {
    formMapRef.current[formId] = { isDirty: isDirty, save };
  };

  const unregisterForm = (formId: string) => {
    delete formMapRef.current[formId];
  };

  const setFormDirty = (formId: string, isDirty: boolean) => {
    if (formMapRef.current[formId]) {
      formMapRef.current[formId] = {
        ...formMapRef.current[formId],
        isDirty: isDirty,
      };
    }
  };

  const getDirtyForms = () => {
    return Object.entries(formMapRef.current)
      .filter(([_, { isDirty }]) => isDirty)
      .map(([formId, { save }]) => ({ formId, save }));
  };

  const requestTransition = (nextAction: () => void) => {
    const dirty = getDirtyForms();
    if (dirty.length > 0) {
      nextActionRef.current = nextAction;
      setIsModalOpen(true); // Open modal if there are dirty forms
    } else {
      nextAction(); // Proceed immediately
    }
  };

  const onSaveAll = async () => {
    const dirty = getDirtyForms();
    for (const { save } of dirty) {
      await save(); // Save all dirty forms
    }
    setIsModalOpen(false);
    // nextActionRef.current?.(); // Proceed after saving
  };

  const onDiscardAll = () => {
    setIsModalOpen(false);
    nextActionRef.current?.(); // Proceed without saving
  };

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return {
    registerForm,
    unregisterForm,
    setFormDirty,
    getDirtyForms,
    requestTransition,
    onSaveAll,
    onDiscardAll,
    isModalOpen,
    openModal,
    closeModal,
  };
};

/**
 * Provides access to the multi-form guard logic through context
 */
export const MultiFormGuardProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useMultiFormGuardProvider();
  return <MultiFormGuardContext.Provider value={value}>{children}</MultiFormGuardContext.Provider>;
};

/**
 * Access the multi-form guard context (must be used inside provider)
 */
export const useMultiFormGuardContext = () => {
  const ctx = useContext(MultiFormGuardContext);
  if (!ctx) throw new Error('Must be used within MultiFormGuardProvider');
  return ctx;
};
