import { UserRole } from '@/generated/graphql';

export enum AppRoutePaths {
  USERS = '/users',
  SETTINGS = '/settings',
  SIGN_IN = '/auth/sign-in',
  FORGOT_PASSWORD = '/auth/forgot-password',
  RESET_PASSWORD = '/auth/reset-password',
  COMPLETE_SIGN_UP = '/auth/complete-sign-up',
  UNSUBSCRIBE_EMAIL = '/email-subscription/unsubscribe',
  CLUBS = '/clubs',
  ASSOCIATIONS = '/associations',
}

export const MOBILE_DEVICE_WIDTH = 640;

export const DATE_FORMAT = 'LLL dd, y';

export const DATE_FORMAT_API = 'yyyy-MM-dd';

export const DATE_FORMAT_MM_DD_YYYY = 'MM/dd/yyyy';

export enum DashboardUserRole {
  Admin = UserRole.Admin,
  Editor = UserRole.Editor,
}

export const TOAST_DURATION = 3000;

export const ZOOM_LEVEL_THRESHOLD = 125;
