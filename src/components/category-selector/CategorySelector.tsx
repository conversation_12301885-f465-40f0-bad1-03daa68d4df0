import { useState } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Command, CommandGroup, CommandItem } from '@/components/ui/Command';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { ClubCategoryEnum } from '@/generated/graphql';
import { cn } from '@/lib/utils';

interface CategorySelectorProps {
  selectedCategory: ClubCategoryEnum | null;
  setSelectedCategory: (category: ClubCategoryEnum | null) => void;
}

export function CategorySelector({ selectedCategory, setSelectedCategory }: CategorySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const categories = [
    { value: ClubCategoryEnum.Creative, label: 'Creative' },
    { value: ClubCategoryEnum.FitnessOutdoor, label: 'Fitness & Outdoor' },
    { value: ClubCategoryEnum.FoodDrink, label: 'Food & Drink' },
    { value: ClubCategoryEnum.Hobbies, label: 'Hobbies' },
    { value: ClubCategoryEnum.SocialFamily, label: 'Social & Family' },
  ];

  const displayedCategory = categories.find((cat) => cat.value === selectedCategory);

  return (
    <Popover modal={false} open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={isOpen}
          className={cn(
            'w-full justify-between text-[#191E3B] font-normal p-3 border border-gray-200 rounded-md text-sm bg-white',
            {
              'text-muted-foreground': !displayedCategory,
            }
          )}
        >
          {displayedCategory ? displayedCategory.label : 'Club Category'}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-[--radix-popover-trigger-width] p-0'
        onOpenAutoFocus={(event) => event.preventDefault()}
      >
        <Command shouldFilter={false}>
          <CommandGroup>
            <ScrollArea className='max-h-64 !overflow-y-auto'>
              {categories.map((category) => (
                <CommandItem
                  key={category.value}
                  value={category.label}
                  onSelect={() => {
                    setSelectedCategory(category.value);
                    setIsOpen(false);
                  }}
                  className='flex items-center justify-between'
                >
                  <span>{category.label}</span>
                  {category.value === selectedCategory && (
                    <Check className='h-4 w-4 text-primary' />
                  )}
                </CommandItem>
              ))}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
