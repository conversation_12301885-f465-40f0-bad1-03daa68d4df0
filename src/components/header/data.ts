import { AppRoutePaths } from '@/lib/constants';

export interface IMenu {
  name: string;
  url: string;
}

export enum EMenuName {
  USERS = 'Users',
  ASSOCIATIONS = 'Associations',
  CLUBS = 'Clubs',
  SETTINGS = 'Settings',
}

export const menuList: IMenu[] = [
  {
    name: EMenuName.USERS,
    url: AppRoutePaths.USERS,
  },
  {
    name: EMenuName.ASSOCIATIONS,
    url: AppRoutePaths.ASSOCIATIONS,
  },
  {
    name: EMenuName.CLUBS,
    url: AppRoutePaths.CLUBS,
  },
  {
    name: EMenuName.SETTINGS,
    url: AppRoutePaths.SETTINGS,
  },
];
