import { Button } from '@/components/ui/Button';
import { Form, FormLabel } from '@/components/ui/form/Form';
import { FormInput } from '@/components/ui/form/FormInput';
import { useResetPasswordMutation, useCompleteSignupMutation } from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';
import { Link, useNavigate } from 'react-router-dom';
import { AppRoutePaths } from '@/lib/constants';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { z } from 'zod';
import { ApolloError } from '@apollo/client';

const passwordFormSchema = z
  .object({
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/[A-Za-z]/, 'Password must contain at least one letter')
      .regex(/[0-9]/, 'Password must contain at least one number')
      .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type PasswordFormValues = z.infer<typeof passwordFormSchema>;

const defaultValues: PasswordFormValues = {
  password: '',
  confirmPassword: '',
};

interface PasswordFormProps {
  email: string;
  otp: string;
  mode: 'reset' | 'complete';
  setIsPasswordCreated?: (isPasswordCreated: boolean) => void;
}

export const PasswordForm = ({ email, otp, mode, setIsPasswordCreated }: PasswordFormProps) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { setAuthState } = useAuthContext();
  const [resetPassword, { loading: resetLoading }] = useResetPasswordMutation();
  const [completeSignup, { loading: completeLoading }] = useCompleteSignupMutation();

  const loading = mode === 'reset' ? resetLoading : completeLoading;

  const onSubmit = async (values: PasswordFormValues) => {
    try {
      if (mode === 'reset') {
        const response = await resetPassword({
          variables: {
            input: {
              email,
              otp,
              newPassword: values.password,
            },
          },
        });

        if (response.data?.resetPassword.message && setIsPasswordCreated) {
          setIsPasswordCreated(true);
        }
      } else {
        const response = await completeSignup({
          variables: {
            input: {
              email,
              otp,
              newPassword: values.password,
            },
          },
        });

        if (response.data?.completeSignup) {
          // Handle successful signup completion like login
          const authToken = response.data.completeSignup;
          localStorage.setItem('auth', JSON.stringify(authToken));
          setAuthState(true, authToken);
          navigate(AppRoutePaths.USERS, { state: { from: AppRoutePaths.COMPLETE_SIGN_UP } });
        }
      }
    } catch (error) {
      if (error instanceof ApolloError) {
        return toast({
          variant: 'destructive',
          title: error.message,
        });
      }
      console.error(`${mode === 'reset' ? 'Reset password' : 'Complete signup'} error:`, error);
    }
  };

  return (
    <div className='w-full sm:max-w-[360px]'>
      <div className='gap-3 flex flex-col'>
        <h1 className='text-2xl sm:text-4xl text-gray-900 font-semibold mb-1'>
          {mode === 'reset' ? 'Create a New Password' : 'Welcome to Unity Admin Dashboard!'}
        </h1>

        <p className='text-base'>
          {mode === 'reset'
            ? 'Your new password must be at least 8 characters and include a mix of letters, numbers, and symbols.'
            : 'Please set your password. It must be at least 8 characters and include a mix of letters, numbers, and symbols.'}
        </p>
      </div>

      <Form
        mode='onChange'
        onSubmit={(values) => onSubmit(values)}
        schema={passwordFormSchema}
        defaultValues={defaultValues}
        className='w-full'
      >
        <div className='space-y-4 sm:space-y-8 mt-8'>
          <div className='flex flex-col gap-1'>
            <FormLabel className='text-sm font-medium text-gray-700 block'>
              {mode === 'reset' ? 'New Password' : 'Password'}
            </FormLabel>
            <FormInput
              name='password'
              type='password'
              placeholder='Enter new password'
              className='w-full'
              showPasswordToggle
            />
          </div>

          <div className='flex flex-col gap-1'>
            <FormLabel className='text-sm font-medium text-gray-700 block'>
              {mode === 'reset' ? 'Confirm New Password' : 'Confirm Password'}
            </FormLabel>
            <FormInput
              name='confirmPassword'
              type='password'
              placeholder='Confirm password'
              className='w-full'
              showPasswordToggle
            />
          </div>

          <Button
            disableOnInvalid
            className='w-full mt-2'
            type='submit'
            disabled={loading}
            loading={loading}
          >
            {mode === 'reset' ? 'Save new password' : 'Create Account & Continue'}
          </Button>

          {mode === 'complete' && (
            <div className='flex justify-center'>
              <span className='text-sm '>
                Already set up ?{' '}
                <Link to={AppRoutePaths.SIGN_IN} className='text-sm text-primary font-semibold'>
                  Go to log in
                </Link>
              </span>
            </div>
          )}
        </div>
      </Form>
    </div>
  );
};
