import { DateRange } from 'react-day-picker';
import { Calendar } from '../ui/Calendar';
import { Button } from '../ui/Button';
import { useState } from 'react';
import { format } from 'date-fns';
import { useIsMobile } from '@/hooks/use-mobile';
import { DATE_FORMAT } from '@/lib/constants';

interface DateRangePickerProps {
  date: DateRange | undefined;
  setDate: (date: DateRange | undefined) => void;
  onConfirm: (date: DateRange | undefined) => void;
  onCancel: () => void;
}

const DateRangePicker = ({ date, onConfirm, onCancel }: DateRangePickerProps) => {
  const isMobile = useIsMobile();

  const [selectedDate, setSelectedDate] = useState<DateRange | undefined>(date);
  return (
    <div className='relative'>
      {isMobile && (
        <div className='w-full flex justify-center pt-2 px-2 items-center gap-2'>
          <Button variant='outline' className='text-[16px] flex-1 font-normal cursor-default'>
            {selectedDate?.from ? format(selectedDate.from, DATE_FORMAT) : 'Start Date'}
          </Button>
          <span className='text-sm text-gray-500'>-</span>
          <Button variant={'outline'} className='text-[16px] flex-1 font-normal cursor-default'>
            {selectedDate?.to ? format(selectedDate.to, DATE_FORMAT) : 'End Date'}
          </Button>
        </div>
      )}
      <Calendar
        initialFocus
        mode='range'
        numberOfMonths={isMobile ? 1 : 2}
        // if mobile, default month is current month, otherwise default month is previous month
        defaultMonth={
          isMobile ? new Date() : new Date(new Date().setMonth(new Date().getMonth() - 1))
        }
        selected={selectedDate}
        onSelect={setSelectedDate}
        className='sm:pb-0'
        classNames={{
          months: 'sm:px-2 sm:flex sm:gap-4 ',
          month: 'sm:first:border-r ',
        }}
      />
      <div className='flex items-center p-4 border border-t justify-between gap-2'>
        <div className=' hidden sm:flex flex-1 items-center gap-2'>
          <Button variant='outline' className='text-[16px] font-normal cursor-default'>
            {selectedDate?.from ? format(selectedDate.from, DATE_FORMAT) : 'Start Date'}
          </Button>
          <span className='text-sm text-gray-500'>-</span>
          <Button variant={'outline'} className='text-[16px] font-normal cursor-default'>
            {selectedDate?.to ? format(selectedDate.to, DATE_FORMAT) : 'End Date'}
          </Button>
        </div>
        <div className='flex flex-1 justify-center sm:justify-end items-center gap-2'>
          <Button variant='outline' className='flex-1' onClick={onCancel}>
            Cancel
          </Button>
          <Button
            className='flex-1'
            disabled={!selectedDate?.from && !selectedDate?.to}
            onClick={() => onConfirm(selectedDate)}
          >
            Confirm
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DateRangePicker;
