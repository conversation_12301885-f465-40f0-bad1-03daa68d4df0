import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Mail } from 'lucide-react';
import { Form } from '@/components/ui/form/Form';
import { FormInput } from '@/components/ui/form/FormInput';
import { DashboardUserRole } from '@/lib/constants';
import { emailSchema } from '@/lib/schemas';
import PermissionCard from './PermissionCard';

const defaultSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .transform((val) => val.trim()),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .transform((val) => val.trim()),
  email: emailSchema,
  role: z.nativeEnum(DashboardUserRole),
});

export type DashboardUserFormSchema = z.infer<typeof defaultSchema>;

interface DashboardUserFormProps {
  onConfirm: (data: DashboardUserFormSchema) => void;
  onCancel: () => void;
  initialValues?: Partial<DashboardUserFormSchema>;
  isEdit?: boolean;
  schema?: z.ZodSchema<DashboardUserFormSchema>;
  title?: string;
  isLoading?: boolean;
}

const DashboardUserForm = ({
  onConfirm,
  onCancel,
  initialValues = {},
  isEdit = false,
  schema = defaultSchema,
  isLoading = false,
}: DashboardUserFormProps) => {
  const onSubmit = (data: DashboardUserFormSchema) => {
    onConfirm(data);
  };

  const defaultValues: DashboardUserFormSchema = {
    firstName: '',
    lastName: '',
    email: '',
    role: DashboardUserRole.Editor,
  };

  const formValues = {
    ...defaultValues,
    ...initialValues,
  };

  return (
    <Form
      mode='onChange'
      schema={schema}
      onSubmit={onSubmit}
      defaultValues={formValues}
      className='sm:space-y-4 space-y-2'
    >
      <div className='grid grid-cols-2 gap-3'>
        <FormInput
          name='firstName'
          label='First Name'
          placeholder='First Name'
          className='placeholder:text-gray-500 truncate'
        />
        <FormInput
          name='lastName'
          label='Last Name'
          placeholder='Last Name'
          className='placeholder:text-gray-500 truncate'
        />
      </div>
      <FormInput
        StartIcon={Mail}
        name='email'
        label='Email'
        placeholder='<EMAIL>'
        className='placeholder:text-gray-500'
      />

      <p className='text-base font-medium text-gray-900'>Permissions</p>

      <div className='flex flex-col gap-4'>
        <PermissionCard role={DashboardUserRole.Editor} name='role' />
        <PermissionCard role={DashboardUserRole.Admin} name='role' />
      </div>

      <div className='flex justify-center w-full gap-3 sm:!mt-6 !mt-4'>
        <Button
          variant='outline'
          className='w-full text-base font-semibold'
          type='button'
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          loading={isLoading}
          className='w-full text-base font-semibold'
          type='submit'
          disabled={isLoading}
          disableOnInvalid
          disabledOnNotDirty
        >
          {isEdit ? 'Update' : 'Invite User'}
        </Button>
      </div>
    </Form>
  );
};

export default DashboardUserForm;
