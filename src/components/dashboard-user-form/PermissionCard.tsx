import { Input } from '@/components/ui/Input';
import { useController } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { cn } from '@/lib/utils';
import { DashboardUserRole } from '@/lib/constants';
import { Circle } from 'lucide-react';
interface PermissionCardProps {
  role: DashboardUserRole;
  name: string;
  defaultRole?: DashboardUserRole;
}

const PermissionCard = ({ role, name, defaultRole }: PermissionCardProps) => {
  const { setValue, control } = useFormContext();
  const { field } = useController({ control, name });
  const checked = field.value === role;
  return (
    <div className='flex items-center justify-center w-full h-full'>
      <label className='cursor-pointer w-full h-full'>
        <Input
          type='radio'
          className='peer sr-only'
          defaultChecked={checked}
          onChange={() => {
            setValue(name, role, { shouldDirty: defaultRole !== role });
          }}
          name={name}
        />
        <div
          className={cn(
            'w-full flex gap-3 rounded-md bg-white p-4 ring-2 ring-transparent transition-all hover:shadow text-[#667085] border h-full md:max-h-[106px]',
            checked && 'bg-[#F5F7FF] border-[#D2D9FD]'
          )}
        >
          <div className=''>
            {checked ? (
              <svg
                xmlns='http://www.w3.org/2000/svg'
                width='20'
                height='20'
                viewBox='0 0 24 24'
                fill='none'
                stroke='#6677C3'
                strokeWidth='2'
                strokeLinecap='round'
                strokeLinejoin='round'
                className='lucide lucide-circle-dot-icon lucide-circle-dot'
              >
                <circle cx='12' cy='12' r='10' />
                <circle cx='12' cy='12' r='3' fill='#6677C3' />
              </svg>
            ) : (
              <Circle className='w-5 h-5 text-muted-foreground' />
            )}
          </div>
          <div className='flex flex-1 text-start flex-col gap-0.5 items-start justify-between'>
            <p className={cn('text-base font-medium text-gray-700', checked && 'text-primary')}>
              {role === DashboardUserRole.Admin ? 'Admin' : 'User'}
            </p>
            <p className={cn('text-base text-gray-700 font-normal', checked && 'text-primary')}>
              {role === DashboardUserRole.Admin
                ? 'Full access to dashboard features: add and manage users, edit permissions, and remove accounts.'
                : 'View-only access to user data. Restricted from editing or managing accounts.'}
            </p>
          </div>
        </div>
      </label>
    </div>
  );
};

export default PermissionCard;
