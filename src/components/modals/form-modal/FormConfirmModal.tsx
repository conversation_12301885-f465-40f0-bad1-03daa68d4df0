import AlertTriangleIcon from '@/assets/images/common/alert-triangle.svg';
import { Button } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/Dialog';

/**
 * Component props interface
 */
type ComponentType = {
  isOpen: boolean; // Controls modal visibility
  onOpenChange?: (open: boolean) => void; // Handler for modal open state changes
  onCancel?: () => void; // Handler for cancel action
  onConfirm?: () => void; // Handler for confirm action
  title?: string; // Modal title text
  description?: string; // Modal description text
  isLoading?: boolean; // Loading state for confirm button
  cancelText?: string; // Custom text for cancel button
  confirmText?: string; // Custom text for confirm button
  width?: number; // Custom width for modal
  isHideClose?: boolean; // Option to hide close button
  className?: string; // Additional CSS classes
};

/**
 * FormConfirmModal Component
 * Reusable confirmation dialog with customizable content and actions
 */
const FormConfirmModal = (props: ComponentType) => {
  // Destructure props for easier access
  const {
    isOpen,
    onOpenChange,
    onCancel,
    onConfirm,
    title,
    description,
    isLoading,
    cancelText,
    confirmText,
    width,
    isHideClose,
    className,
  } = props;

  const computedWidth = width || 400;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        hideClose={isHideClose}
        className={`${className} sm:w-[${computedWidth}px] max-w-[90vw] rounded-lg`}
      >
        {/* Modal Header */}
        <DialogHeader>
          {/* Warning Icon */}
          <div className='flex justify-center'>
            <img src={AlertTriangleIcon} alt='' width={48} height={48} />
          </div>
          {/* Modal Title */}
          <DialogTitle className='text-center text-[18px] font-semibold leading-7'>
            {title}
          </DialogTitle>
        </DialogHeader>

        {/* Modal Description */}
        <DialogDescription className='text-center text-[14px] font-normal leading-[20px] space-y-[14px]'>
          {description}
        </DialogDescription>

        {/* Modal Footer with Action Buttons */}
        <DialogFooter>
          {/* Cancel Button */}
          <div className='w-full flex gap-2'>
            <Button onClick={onCancel} variant={'outline'} className='w-1/2' disabled={isLoading}>
              {cancelText || 'Cancel'}
            </Button>

            {/* Confirm Button */}
            <Button className='w-1/2' onClick={onConfirm} loading={isLoading}>
              {confirmText || 'Confirm'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FormConfirmModal;
