import { Button } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/Dialog';
import AlertIcon from '@/assets/images/alert-icon.svg';

interface Props {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  isLoading?: boolean;
  confirmText?: string;
}

const RemoveModal = ({
  isOpen,
  onOpenChange,
  onCancel,
  onConfirm,
  title,
  description,
  isLoading,
  confirmText,
}: Props) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent hideClose className='sm:w-[400px] w-[90vw] rounded-md'>
        <DialogHeader className='flex flex-col gap-2 items-center'>
          <div className='flex items-center gap-2'>
            <img src={AlertIcon} alt='alert' className='w-12 h-12' />
          </div>
          <DialogTitle className='text-gray-900 font-semibold text-base'>{title}</DialogTitle>
          <DialogDescription className='text-center text-gray-600 text-sm'>
            {description}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className='w-full'>
          <div className='flex flex-1 justify-center gap-2 sm:gap-4 mt-4'>
            <Button
              className='w-full text-gray-700 font-semibold text-base'
              variant={'outline'}
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              className='w-full text-white text-base'
              variant={'destructive'}
              onClick={onConfirm}
              disabled={isLoading}
              loading={isLoading}
            >
              {confirmText || 'Delete'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RemoveModal;
