import LoadingSpinner, { SpinnerProps } from '.';

interface LoadingOverlayProps extends SpinnerProps {
  isLoading: boolean;
}

const LoadingOverlay = ({ isLoading, className }: LoadingOverlayProps) => {
  return isLoading ? (
    <div className='absolute top-0 left-0 z-10 flex items-center justify-center w-full h-full backdrop-blur-sm overflow-hidden'>
      <LoadingSpinner className={className} />
    </div>
  ) : null;
};

export default LoadingOverlay;
