import React, { useCallback } from 'react';
import { ClubCategoryEnum } from '@/generated/graphql';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/Command';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { ChevronDown, Check as CheckIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CategorySelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const CategorySelect: React.FC<CategorySelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select category...',
  className,
  disabled = false,
}) => {
  const handleSelectCategory = useCallback(
    (category: string) => {
      onValueChange(category);
    },
    [onValueChange]
  );

  const getCategoryLabel = (category: ClubCategoryEnum) => {
    switch (category) {
      case ClubCategoryEnum.Creative:
        return 'Creative';
      case ClubCategoryEnum.FitnessOutdoor:
        return 'Fitness & Outdoor';
      case ClubCategoryEnum.FoodDrink:
        return 'Food & Drink';
      case ClubCategoryEnum.Hobbies:
        return 'Hobbies';
      case ClubCategoryEnum.SocialFamily:
        return 'Social & Family';
      default:
        return 'N/A';
    }
  };

  const categories = [
    ClubCategoryEnum.Creative,
    ClubCategoryEnum.FitnessOutdoor,
    ClubCategoryEnum.FoodDrink,
    ClubCategoryEnum.Hobbies,
    ClubCategoryEnum.SocialFamily,
  ];

  return (
    <Popover modal={false}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          disabled={disabled}
          className={cn(
            'w-full justify-between text-[#191E3B] font-normal p-3 border border-gray-200 rounded-md text-sm bg-white',
            {
              'text-muted-foreground': value === 'All Categories',
            },
            className
          )}
        >
          {value === 'All Categories' ? placeholder : getCategoryLabel(value as ClubCategoryEnum)}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-[--radix-popover-trigger-width] p-0'
        onOpenAutoFocus={(e) => e.preventDefault()}
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        <Command>
          <CommandInput placeholder='Search category...' />
          <CommandEmpty>No category found.</CommandEmpty>
          <CommandGroup>
            <ScrollArea className='max-h-64'>
              <CommandItem
                value='All Categories'
                onSelect={() => handleSelectCategory('All Categories')}
                className='flex items-center justify-between'
              >
                <span>All Categories</span>
                {value === 'All Categories' && <CheckIcon className='h-4 w-4 text-primary' />}
              </CommandItem>
              {categories.map((category) => (
                <CommandItem
                  key={category}
                  value={category}
                  onSelect={() => handleSelectCategory(category)}
                  className='flex items-center justify-between'
                >
                  <span>{getCategoryLabel(category)}</span>
                  {value === category && <CheckIcon className='h-4 w-4 text-primary' />}
                </CommandItem>
              ))}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default CategorySelect;
