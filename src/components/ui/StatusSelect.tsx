import React, { useCallback } from 'react';
import { ClubRequestStatus } from '@/generated/graphql';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/Command';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { ChevronDown, Check as CheckIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatusSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const StatusSelect: React.FC<StatusSelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select status...',
  className,
  disabled = false,
}) => {
  const handleSelectStatus = useCallback(
    (status: string) => {
      onValueChange(status);
    },
    [onValueChange]
  );

  const getStatusLabel = (status: ClubRequestStatus) => {
    switch (status) {
      case ClubRequestStatus.Pending:
        return 'Pending';
      case ClubRequestStatus.Approved:
        return 'Approved';
      case ClubRequestStatus.Rejected:
        return 'Rejected';
      default:
        return 'Unknown';
    }
  };

  const statuses = [
    ClubRequestStatus.Pending,
    ClubRequestStatus.Approved,
    ClubRequestStatus.Rejected,
  ];

  return (
    <Popover modal={false}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          disabled={disabled}
          className={cn(
            'w-full justify-between text-[#191E3B] font-normal p-3 border border-gray-200 rounded-md text-sm bg-white',
            {
              'text-muted-foreground': value === 'All Status',
            },
            className
          )}
        >
          {value === 'All Status' ? placeholder : value}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-[--radix-popover-trigger-width] p-0'
        onOpenAutoFocus={(e) => e.preventDefault()}
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        <Command>
          <CommandInput placeholder='Search status...' />
          <CommandEmpty>No status found.</CommandEmpty>
          <CommandGroup>
            <ScrollArea className='max-h-32'>
              <CommandItem
                value='All Status'
                onSelect={() => handleSelectStatus('All Status')}
                className='flex items-center justify-between'
              >
                <span>All Status</span>
                {value === 'All Status' && <CheckIcon className='h-4 w-4 text-primary' />}
              </CommandItem>
              {statuses.map((status) => (
                <CommandItem
                  key={status}
                  value={status}
                  onSelect={() => handleSelectStatus(getStatusLabel(status))}
                  className='flex items-center justify-between'
                >
                  <span>{getStatusLabel(status)}</span>
                  {value === getStatusLabel(status) && (
                    <CheckIcon className='h-4 w-4 text-primary' />
                  )}
                </CommandItem>
              ))}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default StatusSelect;
