import { cn } from '@/lib/utils';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import React from 'react';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  showPassword?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, showPassword = false, ...props }, ref) => {
    const [isShowPassword, setIsShowPassword] = React.useState(showPassword);

    const togglePasswordVisibility = () => setIsShowPassword(!isShowPassword);

    const inputClasses = React.useMemo(
      () =>
        cn(
          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          type === 'password' && 'pr-16',
          className
        ),
      [type, className]
    );

    return (
      <div className='w-full relative'>
        <input
          type={isShowPassword ? 'text' : type}
          className={inputClasses}
          ref={ref}
          {...props}
        />
        {type === 'password' && showPassword && (
          <div className='absolute right-0 flex items-center pr-3 -translate-y-1/2 top-1/2 gap-x-1'>
            {isShowPassword ? (
              <EyeOffIcon className='cursor-pointer' onClick={togglePasswordVisibility} size={20} />
            ) : (
              <EyeIcon className='cursor-pointer' onClick={togglePasswordVisibility} size={20} />
            )}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input };
