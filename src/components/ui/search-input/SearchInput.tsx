import * as z from 'zod';
import { Search } from 'lucide-react';
import { Form } from '../form/Form';
import { FormInput } from '../form/FormInput';
import { useMemo } from 'react';

const schema = z.object({
  search: z.string().optional(),
});

interface SearchInputProps {
  name: string;
  onChange: (search: string) => void;
  value: string;
}

const SearchInput = ({ name, onChange, value }: SearchInputProps) => {
  const defaultValues = useMemo(() => {
    return {
      search: value,
    };
  }, [value]);

  return (
    <Form className='w-full' schema={schema} defaultValues={defaultValues} onSubmit={() => {}}>
      <FormInput
        name={name}
        type='text'
        placeholder='Search'
        StartIcon={Search}
        iconClassName='w-5 h-5'
        className='w-full sm:max-w-xs '
        onChange={(e) => {
          onChange(e.target.value);
        }}
      />
    </Form>
  );
};
export default SearchInput;
