'use client';

import { cn } from '@/lib/utils';
import { useToast } from '../../hooks/useToast';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from './Toast';
import { TOAST_DURATION } from '@/lib/constants';

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, variant, ...props }) {
        return (
          <Toast key={id} variant={variant} duration={TOAST_DURATION} {...props}>
            <div className='flex items-center gap-3'>
              {variant === 'success' && (
                <div className='flex text-xs font-medium shrink-0 bg-white border border-[#A6F4C5] py-0.5 px-2 rounded-full '>
                  Success
                </div>
              )}
              {variant === 'destructive' && (
                <div className='flex text-destructive text-xs font-medium shrink-0 bg-white border border-[#FECDCA] py-0.5 px-2 rounded-full '>
                  Error
                </div>
              )}
              <div className='grid gap-1'>
                {title && <ToastTitle className='text-xs font-medium'>{title}</ToastTitle>}
                {description && <ToastDescription>{description}</ToastDescription>}
              </div>
            </div>
            {action}
            <ToastClose
              className={cn(
                variant === 'destructive' && 'text-destructive hover:text-destructive',
                variant === 'success' && 'text-success'
              )}
            />
          </Toast>
        );
      })}
      <ToastViewport position={toasts[0]?.position || 'top-center'} className='z-[101]' />
    </ToastProvider>
  );
}
