'use client';

import * as React from 'react';
import * as SelectPrimitive from '@radix-ui/react-select';
import { Check, ChevronDown, Loader2 } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Controller, useFormContext } from 'react-hook-form';
import { ErrorMessage } from '@hookform/error-message';

export type SelectProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root> & {
  name: string;
  placeholder: string;
  isShowError?: boolean;
  className?: string;
  contentClassName?: string;
  defaultValue?: string | number;
  isLoading?: boolean;
};

const FormSelect = React.forwardRef<React.ElementRef<typeof SelectPrimitive.Root>, SelectProps>(
  (
    {
      children,
      isShowError = true,
      name,
      placeholder,
      className,
      contentClassName,
      isLoading,
      ...props
    },
    _ref
  ) => {
    const { control, formState } = useFormContext();
    return (
      <Controller
        control={control}
        name={name}
        render={({ field, fieldState }) => (
          <SelectPrimitive.Root
            value={field.value}
            onValueChange={(value) => {
              field.onChange(value);
            }}
            defaultValue={field.value}
            {...props}
          >
            <div className='relative w-full'>
              {isLoading ? (
                <div className='absolute top-1/2 right-0 -translate-x-1/2 -translate-y-1/2'>
                  <Loader2 className='h-4 w-4 animate-spin opacity-50' />{' '}
                </div>
              ) : null}
              <SelectTrigger
                className={cn(
                  'overflow-hidden whitespace-nowrap',
                  field.value ? '' : 'text-placeholder',
                  formState.errors[name] ? 'border-red-500 focus-visible:ring-red-500' : '',
                  className
                )}
                isLoading={isLoading}
              >
                <SelectValue
                  placeholder={placeholder}
                  className='flex-1 overflow-ellipsis text-primary '
                />
              </SelectTrigger>
              <SelectContent
                className={cn('max-w-min w-[--radix-select-trigger-width]', contentClassName)}
              >
                {children}
              </SelectContent>
            </div>
            {isShowError && fieldState.error && (
              <div>
                <ErrorMessage
                  name={name}
                  render={({ message }) => {
                    return <span className='text-red-500 text-sm'>{message}</span>;
                  }}
                />
              </div>
            )}
          </SelectPrimitive.Root>
        )}
      />
    );
  }
);

FormSelect.displayName = 'FormSelect';

const SelectGroup = SelectPrimitive.Group;

const SelectValue = SelectPrimitive.Value;

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & { isLoading?: boolean }
>(({ className, children, isLoading, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-placeholder focus:outline-none focus:ring-2 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-primary-foreground [&>span]:text-ellipsis [&>span]:overflow-hidden',
      className
    )}
    {...props}
  >
    {children}
    {!isLoading && (
      <SelectPrimitive.Icon asChild>
        <ChevronDown className='h-4 w-4 min-w-4 opacity-50' />
      </SelectPrimitive.Icon>
    )}
  </SelectPrimitive.Trigger>
));
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName;

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = 'popper', ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
        position === 'popper' &&
          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',
        className
      )}
      position={position}
      {...props}
    >
      <SelectPrimitive.Viewport
        className={cn(
          position === 'popper' &&
            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]'
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
));
SelectContent.displayName = SelectPrimitive.Content.displayName;

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn('py-1.5 pl-8 pr-2 text-sm font-semibold', className)}
    {...props}
  />
));
SelectLabel.displayName = SelectPrimitive.Label.displayName;

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      'relative flex [&>span]:text-ellipsis [&>span]:overflow-hidden justify-between w-full cursor-default select-none items-center rounded-sm px-3 py-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className
    )}
    {...props}
  >
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>

    <SelectPrimitive.ItemIndicator asChild>
      <Check className='h-4 w-4' />
    </SelectPrimitive.ItemIndicator>
  </SelectPrimitive.Item>
));
SelectItem.displayName = SelectPrimitive.Item.displayName;

export {
  FormSelect,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
};
