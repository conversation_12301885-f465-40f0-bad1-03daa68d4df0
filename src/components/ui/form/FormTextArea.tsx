import { cn } from '@/lib/utils';
import { useFormContext } from 'react-hook-form';
import { Textarea } from '@/components/ui/TextArea';
import { forwardRef } from 'react';
import { FormField, FormItem } from './Form';

interface FormTextAreaProps {
  name: string;
  placeholder?: string;
  rows?: number;
  className?: string;
  required?: boolean;
  disabled?: boolean;
  formItemClassName?: string;
  formLabelClassName?: string;
}

const FormTextArea = forwardRef<HTMLTextAreaElement, FormTextAreaProps>(
  ({ name, placeholder, rows = 4, className, required, disabled, formItemClassName }, ref) => {
    const { control } = useFormContext();

    return (
      <FormField
        control={control}
        name={name}
        render={({ field, formState: { errors } }) => {
          const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
            const value = event.target?.value?.trimStart();
            field.onChange(value);
          };

          return (
            <FormItem
              className={cn(
                'flex flex-col space-y-2 w-full',
                errors[name] && 'is-error group',
                formItemClassName
              )}
            >
              <Textarea
                {...field}
                ref={ref}
                placeholder={placeholder}
                rows={rows}
                className={cn(
                  'resize-none w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm transition-colors placeholder:text-muted-foreground placeholder:text-base disabled:cursor-not-allowed disabled:opacity-50 md:text-sm disabled:bg-gray-100 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#F4EBFF] focus-visible:ring-offset-0',
                  errors[name] && 'border-red-500 focus-visible:ring-red-500',
                  className
                )}
                required={required}
                disabled={disabled}
                onChange={handleChange}
              />
              {errors[name] && (
                <p className='text-xs text-red-500'>{errors[name]?.message as string}</p>
              )}
            </FormItem>
          );
        }}
      />
    );
  }
);

FormTextArea.displayName = 'FormTextArea';

export { FormTextArea };
