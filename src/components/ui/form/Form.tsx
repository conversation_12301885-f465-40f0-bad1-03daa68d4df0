import { zodResolver } from '@hookform/resolvers/zod';
import type * as LabelPrimitive from '@radix-ui/react-label';
import { Slot } from '@radix-ui/react-slot';
import * as React from 'react';
import type {
  ControllerProps,
  DefaultValues,
  FieldPath,
  FieldValues,
  Mode,
  UseFormProps,
  UseFormReturn,
} from 'react-hook-form';
import { Controller, FormProvider, useForm, useFormContext } from 'react-hook-form';
import type { Except } from 'type-fest';
import type { z } from 'zod';

import { cn } from '@/lib/utils';
import { Label } from './Label';

import FormConfirmModal from '@/components/modals/form-modal/FormConfirmModal';
import { useBlocker } from 'react-router-dom';

export type FormProps<
  TSchema extends z.ZodTypeAny,
  TDefaultValues extends z.infer<TSchema>,
> = Except<React.InputHTMLAttributes<HTMLFormElement>, 'onSubmit'> & {
  schema?: TSchema;
  onSubmit: (data: z.infer<TSchema>, formHandler?: UseFormReturn<z.TypeOf<TSchema>>) => void;
  forminstance?: UseFormReturn<z.infer<TSchema>>;
  defaultValues?: DefaultValues<TDefaultValues>;
  mode?: Mode;
  isSubmitting?: boolean;
  formProps?: Omit<UseFormProps<z.infer<TSchema>>, 'mode' | 'resolver' | 'defaultValues'>;
  showUnsavedChangesConfirmModal?: boolean;
};

const Form = <TSchema extends z.ZodTypeAny, TDefaultValues extends z.infer<TSchema>>(
  props: FormProps<TSchema, TDefaultValues>
) => {
  const {
    schema,
    children,
    onSubmit,
    mode,
    defaultValues,
    className,
    isSubmitting,
    formProps = { shouldFocusError: false },
    forminstance,
    showUnsavedChangesConfirmModal = false,
    ...rest
  } = props;

  const [isOpenConfirmModal, setIsOpenConfirmModal] = React.useState(false);

  const memoizedDefaultValues = React.useMemo(() => defaultValues, [defaultValues]);

  const internalForm = useForm<z.infer<TSchema>>({
    ...formProps,
    mode: mode ?? 'onBlur',
    resolver: schema && zodResolver(schema),
    defaultValues: memoizedDefaultValues,
  });

  const formHandler = forminstance ?? internalForm;

  const isValid =
    formHandler.formState.isValid && Object.keys(formHandler.formState.errors).length === 0;

  const isDirty = Object.keys(formHandler.formState.dirtyFields).length > 0;

  // if form is dirty and user try to leave the page, show unsaved changes modal
  const blocker = useBlocker(({ currentLocation, nextLocation }) => {
    return (
      isDirty &&
      showUnsavedChangesConfirmModal &&
      currentLocation.pathname !== nextLocation.pathname
    );
  });

  React.useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // prevent user from leaving the page if form is dirty
      if (isDirty && showUnsavedChangesConfirmModal) {
        event.preventDefault();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isDirty, showUnsavedChangesConfirmModal]);

  React.useEffect(() => {
    if (blocker.state === 'blocked') {
      setIsOpenConfirmModal(true);
    }
  }, [blocker, setIsOpenConfirmModal]);

  return (
    <FormProvider {...formHandler}>
      <form
        {...rest}
        onSubmit={formHandler.handleSubmit((data: z.infer<TSchema>) => onSubmit(data, formHandler))}
        className={cn(
          className,
          !isValid && 'is-invalid group',
          !isDirty && 'not-dirty group',
          isSubmitting && 'is-loading group'
        )}
      >
        {children}
      </form>
      <FormConfirmModal
        isOpen={isOpenConfirmModal}
        title='Unsaved changes'
        description='Do you want to save or discard changes?'
        cancelText='Discard'
        confirmText='Save changes'
        isHideClose
        width={400}
        onCancel={() => {
          setIsOpenConfirmModal(false);
          if (blocker && typeof blocker.proceed === 'function') {
            blocker.proceed();
          }
        }}
        onConfirm={() => {
          if (!isValid) {
            return setIsOpenConfirmModal(false);
          }
          formHandler.handleSubmit((data: z.infer<TSchema>) => {
            onSubmit(data, formHandler);
            formHandler.reset();
            setIsOpenConfirmModal(false);
            if (blocker && typeof blocker.reset === 'function') {
              blocker.reset();
            }
          })();
        }}
      />
    </FormProvider>
  );
};

Form.displayName = 'Form';

// ------------------------------------------------------------------------------------------------
// Form Field Context & Component
// ------------------------------------------------------------------------------------------------
type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  name: TName;
};

const FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);

const FormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  ...props
}: ControllerProps<TFieldValues, TName>) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
};

const useFormField = () => {
  const fieldContext = React.useContext(FormFieldContext);
  const itemContext = React.useContext(FormItemContext);
  const { getFieldState, formState } = useFormContext();

  const fieldState = getFieldState(fieldContext.name, formState);

  if (!fieldContext) {
    throw new Error('useFormField should be used within <FormField>');
  }

  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  };
};

// ------------------------------------------------------------------------------------------------
// Form Item Context & Component
// ------------------------------------------------------------------------------------------------
type FormItemContextValue = {
  id: string;
};

const FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);

const FormItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    const id = React.useId();

    return (
      <FormItemContext.Provider value={{ id }}>
        <div ref={ref} className={cn('space-y-2', className)} {...props} />
      </FormItemContext.Provider>
    );
  }
);
FormItem.displayName = 'FormItem';

// ------------------------------------------------------------------------------------------------
// Form Label Component
// ------------------------------------------------------------------------------------------------
const FormLabel = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> & {
    showCustomError?: boolean;
  }
>(({ className, children, showCustomError, ...props }, ref) => {
  const { error, formItemId } = useFormField();

  return (
    <Label
      ref={ref}
      htmlFor={formItemId}
      {...props}
      className={cn([error && 'flex justify-between', !children && 'flex justify-end'], className)}
    >
      {children}
      {error && (
        <span className='text-[13px] font-normal text-error'>
          {showCustomError ? error.message : 'This field is not valid'}
        </span>
      )}
    </Label>
  );
});
FormLabel.displayName = 'FormLabel';

// ------------------------------------------------------------------------------------------------
// Form Control Component
// ------------------------------------------------------------------------------------------------
const FormControl = React.forwardRef<
  React.ElementRef<typeof Slot>,
  React.ComponentPropsWithoutRef<typeof Slot>
>(({ ...props }, ref) => {
  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}
      aria-invalid={!!error}
      {...props}
    />
  );
});
FormControl.displayName = 'FormControl';

// ------------------------------------------------------------------------------------------------
// Form Description Component
// ------------------------------------------------------------------------------------------------
const FormDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormField();

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn('text-[0.8rem] text-muted-foreground', className)}
      {...props}
    />
  );
});
FormDescription.displayName = 'FormDescription';

// ------------------------------------------------------------------------------------------------
// Form Message Component
// ------------------------------------------------------------------------------------------------
const FormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormField();
  const body = error ? String(error?.message) : children;

  if (!body) {
    return null;
  }

  return (
    <p
      ref={ref}
      id={formMessageId}
      className={cn('text-[0.8rem] font-medium text-destructive', className)}
      {...props}
    >
      {body}
    </p>
  );
});
FormMessage.displayName = 'FormMessage';

export {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useFormField,
};
