import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import ImageUpload from '@/components/ui/ImageUpload';
import { cn } from '@/lib/utils';

import { FormDescription, FormField, FormItem } from '@/components/ui/form/Form';
import { Label } from './Label';

interface FormImageUploadProps {
  name: string;
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  disabled?: boolean;
  placeholder?: string;
  formItemClassName?: string;
  formLabelClassName?: string;
  accept?: string;
  maxSize?: number;
  allowedTypes?: string[];
}

export function FormImageUpload({
  name,
  label,
  description,
  disabled,
  placeholder = 'Upload Image',
  formItemClassName,
  formLabelClassName,
  accept = 'image/png,image/jpeg,image/jpg,image/svg+xml',
  maxSize = 5 * 1024 * 1024, // 5MB default
  allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'],
}: FormImageUploadProps) {
  const { control, setError, clearErrors } = useFormContext();

  const handleFileChange = (file: File | null, field: any) => {
    if (!file) {
      field.onChange(null);
      return;
    }

    // Clear any existing errors first
    clearErrors(name);

    // Validate file type
    if (!allowedTypes.includes(file.type)) {
      setError(name, {
        type: 'manual',
        message: 'Only PNG, JPEG, and SVG files are allowed',
      });
      return;
    }

    // Validate file size
    if (file.size > maxSize) {
      setError(name, {
        type: 'manual',
        message: 'File size must be less than 5MB',
      });
      return;
    }

    // File is valid, update the field
    field.onChange(file);
  };

  const getImageValue = (fieldValue: any) => {
    // If it's a File object, return it as-is for ImageUpload to handle
    if (fieldValue instanceof File) {
      return fieldValue;
    }
    // If it's a string (URL), return it as-is
    if (typeof fieldValue === 'string') {
      return fieldValue;
    }
    // Otherwise return null
    return null;
  };

  return (
    <FormField
      control={control}
      name={name as any}
      render={({ field, formState: { errors } }) => {
        const fieldError = errors[name];

        return (
          <FormItem
            className={cn(
              'flex flex-col space-y-2 w-full',
              fieldError && 'is-error group',
              formItemClassName
            )}
          >
            {label && (
              <Label className={cn('text-sm font-medium', formLabelClassName)}>{label}</Label>
            )}
            <div>
              <ImageUpload
                value={getImageValue(field.value)}
                onChange={(file) => handleFileChange(file, field)}
                disabled={disabled}
                placeholder={placeholder}
                hasError={!!fieldError}
                errorMessage={fieldError?.message as string}
                accept={accept}
                onInvalidFile={(message) => {
                  setError(name, {
                    type: 'manual',
                    message,
                  });
                }}
              />
            </div>
            {typeof description === 'string' ? (
              <FormDescription>{description}</FormDescription>
            ) : (
              description
            )}
          </FormItem>
        );
      }}
    />
  );
}

export default FormImageUpload;
