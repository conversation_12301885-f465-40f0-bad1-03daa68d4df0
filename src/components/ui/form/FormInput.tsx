import { <PERSON>ert<PERSON><PERSON>cle, Eye, EyeOff, LucideIcon } from 'lucide-react';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { cn } from '@/lib/utils';

import { FormDescription, FormField, FormItem } from '@/components/ui/form/Form';
import { Label } from './Label';

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(
  ({ className, type, placeholder, onBlur, ...props }, ref) => {
    const [plhd, setPlhd] = React.useState(placeholder);
    React.useEffect(() => {
      setPlhd(placeholder);
    }, [placeholder]);

    return (
      <input
        onBlur={(e) => {
          onBlur?.(e);
          setPlhd(placeholder);
        }}
        onFocus={() => setPlhd('')}
        placeholder={plhd}
        type={type}
        className={cn(
          'flex h-11 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground placeholder:text-base focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#F4EBFF] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm disabled:bg-gray-100',
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = 'Input';

export type InputProps = React.ComponentProps<'input'> & {
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  name: string;
  rightIcon?: JSX.Element;
  mask?: (value: string, previousValue?: string) => string;
  hideError?: boolean;
  formItemClassName?: string;
  formLabelClassName?: string;
  StartIcon?: LucideIcon;
  iconClassName?: string;
  showPasswordToggle?: boolean;
};

const FormInput = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      description,
      name,
      rightIcon,
      onChange,
      formItemClassName,
      StartIcon,
      iconClassName,
      showPasswordToggle,
      type,
      label,
      formLabelClassName,
      ...props
    },
    ref
  ) => {
    const { control } = useFormContext();

    const [showPassword, setShowPassword] = React.useState(false);
    const isPasswordField = type === 'password' && showPasswordToggle;
    return (
      <FormField
        control={control}
        name={name}
        render={({ field, formState: { errors } }) => {
          const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
            let value = event.target?.value?.trimStart();
            if (props.mask) {
              value = props.mask(value, field.value);
            }
            field.onChange(value);
            onChange?.(event);
          };

          return (
            <FormItem
              className={cn(
                'flex flex-col space-y-2 w-full',
                errors[name] && 'is-error group',
                formItemClassName
              )}
            >
              {label && (
                <Label className={cn('text-sm font-medium', formLabelClassName)}>{label}</Label>
              )}
              {rightIcon ? (
                <div className='relative'>
                  <Input
                    {...field}
                    className={cn(
                      className,
                      errors[name] && 'border-destructive focus-visible:ring-destructive pr-10'
                    )}
                    {...props}
                    type={isPasswordField && showPassword ? 'text' : type}
                    onChange={handleChange}
                    onCopy={(e) => {
                      if (type === 'password' && !showPassword) {
                        e.preventDefault();
                        return false;
                      }
                    }}
                    ref={ref}
                  />
                  {isPasswordField && (
                    <button
                      type='button'
                      onClick={() => setShowPassword((prev) => !prev)}
                      className='absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700'
                    >
                      {showPassword ? <Eye size={20} /> : <EyeOff size={20} />}
                    </button>
                  )}
                  {!isPasswordField && (
                    <>
                      {errors[name] ? (
                        <AlertCircle className='absolute right-4 top-1/2 -translate-y-1/2 text-destructive' />
                      ) : (
                        <div className='absolute right-4 top-1/2 -translate-y-1/2'>{rightIcon}</div>
                      )}
                    </>
                  )}
                </div>
              ) : (
                <div className='relative'>
                  {StartIcon && (
                    <div className='absolute left-3 top-1/2 transform -translate-y-1/2'>
                      <StartIcon size={18} className={cn('text-muted-foreground', iconClassName)} />
                    </div>
                  )}
                  <Input
                    {...field}
                    className={cn(
                      'flex h-11 w-full rounded-md border border-input bg-background py-2.5 px-3.5 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-0',
                      StartIcon ? 'pl-9' : '',
                      className,
                      errors[name] &&
                        'border-destructive focus-visible:ring-destructive pr-10 focus-visible:ring-1'
                    )}
                    {...props}
                    type={isPasswordField && showPassword ? 'text' : type}
                    onCopy={(e) => {
                      if (type === 'password' && !showPassword) {
                        e.preventDefault();
                        return false;
                      }
                    }}
                    onChange={handleChange}
                    ref={ref}
                  />
                  {isPasswordField && (
                    <button
                      type='button'
                      onClick={() => setShowPassword((prev) => !prev)}
                      className='absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700'
                    >
                      {showPassword ? <Eye size={20} /> : <EyeOff size={20} />}
                    </button>
                  )}
                  {errors[name] && !isPasswordField && (
                    <AlertCircle className='absolute right-4 top-1/2 -translate-y-1/2 text-destructive' />
                  )}
                </div>
              )}
              {errors[name] && (
                <p className='text-[13px] text-destructive mt-1'>
                  {errors[name]?.message
                    ? String(errors[name]?.message)
                    : 'This field is not valid'}
                </p>
              )}
              {typeof description === 'string' ? (
                <FormDescription>{description}</FormDescription>
              ) : (
                description
              )}
            </FormItem>
          );
        }}
      />
    );
  }
);

FormInput.displayName = 'FormInput';

export { FormInput, Input };
