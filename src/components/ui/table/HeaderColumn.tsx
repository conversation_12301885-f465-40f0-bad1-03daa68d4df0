import { PropsWithChildren } from 'react';
import { Column } from '@tanstack/table-core';
import { cn } from '@/lib/utils';
import { ArrowDown, ArrowDownUp, ArrowUp } from 'lucide-react';

const HeaderColumn = <TData,>({
  children,
  column,
}: PropsWithChildren<{ column: Column<TData> }>) => {
  return (
    <div
      onClick={() => {
        column.toggleSorting(column.getIsSorted() === 'asc');
      }}
      className={cn(
        'flex items-center whitespace-nowrap',
        column.getCanSort() ? 'cursor-pointer' : 'pointer-events-none'
      )}
    >
      {children}
      {column.getCanSort() ? (
        column.getIsSorted() ? (
          column.getIsSorted() === 'desc' ? (
            <ArrowUp className='ml-2 h-4 w-4' />
          ) : (
            <ArrowDown className='ml-2 h-4 w-4' />
          )
        ) : (
          <ArrowDownUp className='ml-2 h-4 w-4' />
        )
      ) : null}
    </div>
  );
};

export { HeaderColumn };
