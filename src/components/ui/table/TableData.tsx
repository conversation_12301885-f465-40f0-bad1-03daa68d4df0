import { cn } from '@/lib/utils';
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  OnChangeFn,
  PaginationState,
  Row,
  RowData,
  RowSelectionState,
  SortingState,
  Updater,
  useReactTable,
} from '@tanstack/react-table';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../Table';

declare module '@tanstack/react-table' {
  //allows us to define custom properties for our columns
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData extends RowData, TValue> {
    filterVariant?: 'text' | 'range' | 'select';
    flexGrow?: number;
    padding?: string;
  }
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  isFetching?: boolean;
  isLoading?: boolean;
  queryPageSize?: number;
  queryPageIndex?: number;
  totalItems?: number;
  pagination?: PaginationState;
  sorting?: SortingState;
  filters?: ColumnFiltersState;
  onPaginationChange?: (pagination: Updater<PaginationState>) => void;
  onSortingChange?: OnChangeFn<SortingState>;
  onRowSelectionChange?: OnChangeFn<RowSelectionState>;
  initialRowSelected?: RowSelectionState;
  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>;
  getRowId?: (row: TData) => string;
  onRowClick?: (row: Row<TData>) => void;
}

function TableData<TData, TValue>({
  data,
  columns,
  pagination,
  sorting,
  filters,
  onSortingChange,
  onPaginationChange,
  onRowSelectionChange,
  onColumnFiltersChange,
  initialRowSelected,
  getRowId,
  onRowClick,
}: DataTableProps<TData, TValue>) {
  const selectedRows = initialRowSelected ? initialRowSelected : ({} as RowSelectionState);

  const table = useReactTable({
    data,
    columns,
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    enableRowSelection: true,

    state: {
      pagination: pagination,
      sorting: sorting,
      rowSelection: selectedRows,
      columnFilters: filters,
    },

    getRowId: getRowId,
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: onRowSelectionChange,
    onPaginationChange: (pagination) => onPaginationChange?.(pagination),
    onSortingChange: onSortingChange,
    onColumnFiltersChange: onColumnFiltersChange,
  });

  return (
    <div className='w-full'>
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id} className='!bg-white'>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id} style={{ width: header.getSize() }}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            <>
              {table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => onRowClick?.(row)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      style={{
                        width: cell.column.getSize(),
                        maxWidth: cell.column.getSize(),
                        minWidth: cell.column.getSize(),
                        ...(cell.column.columnDef.meta?.flexGrow && {
                          flexGrow: cell.column.columnDef.meta.flexGrow,
                        }),
                        padding: cell.column.columnDef.meta?.padding,
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </>
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className={cn('h-24 text-center')}>
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

export default TableData;
