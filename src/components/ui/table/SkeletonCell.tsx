import { PropsWithChildren } from 'react';
import { Skeleton } from '@/components/ui/Skeleton';
import { cn } from '@/lib/utils';

type SkeletonCellProps = {
  className?: string;
  isLoading?: boolean;
  skeletonCount?: number;
};

const SkeletonCell = ({
  children,
  className,
  skeletonCount = 1,
  isLoading,
}: PropsWithChildren<SkeletonCellProps>) => {
  if (isLoading) {
    return (
      <div className={cn('flex flex-col gap-2 w-fit h-full', className)}>
        {Array(skeletonCount)
          .fill(null)
          .map((_, index) => (
            <Skeleton key={index} className='h-4 w-[70px] animate-pulse duration-700' />
          ))}
      </div>
    );
  }

  return <div className={cn('h-full', className)}>{children}</div>;
};
export default SkeletonCell;
