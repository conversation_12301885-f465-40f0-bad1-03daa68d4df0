import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import ReactPaginate from 'react-paginate';

interface DataTablePaginationProps {
  pageCount: number;
  onPageChange: (page: number) => void;
  currentPage?: number;
}

const PreviousLabel = () => {
  return (
    <div className='flex items-center gap-2'>
      <ArrowLeft className='h-5 w-5' />
      <span className='text-sm hidden sm:block font-semibold'>Previous</span>
    </div>
  );
};

const NextLabel = () => {
  return (
    <div className='flex items-center gap-2'>
      <span className='text-sm hidden sm:block font-semibold'>Next</span>
      <ArrowRight className='h-5 w-5 ' />
    </div>
  );
};

const MobilePagination = ({
  currentPage,
  pageCount,
}: {
  currentPage: number;
  pageCount: number;
}) => {
  return (
    <div className={cn('text-sm font-medium', !pageCount && 'hidden')}>
      Page {currentPage + 1} of {pageCount}
    </div>
  );
};

export function TablePagination({
  pageCount,
  onPageChange,
  currentPage = 1,
}: DataTablePaginationProps) {
  const isMobile = useIsMobile();
  return (
    <div className='w-full flex items-center justify-between sm:py-2 py-[10px] mt-2 sm:mt-0 px-4'>
      <div className='flex-1'>
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 0}
          className={`flex items-center gap-2 ${currentPage === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <PreviousLabel />
        </button>
      </div>

      <div className='flex-1 flex justify-center'>
        {isMobile ? (
          <MobilePagination currentPage={currentPage} pageCount={pageCount} />
        ) : (
          <ReactPaginate
            pageCount={pageCount}
            marginPagesDisplayed={2}
            onPageChange={(selectedItem) => onPageChange(selectedItem.selected)}
            containerClassName='flex items-center gap-2'
            breakLabel='...'
            forcePage={currentPage}
            activeClassName='bg-primary-foreground text-primary'
            pageClassName='w-10 h-10 p-3 rounded-sm text-[14px] leading-[20px] font-medium flex items-center justify-center'
            pageRangeDisplayed={2}
            previousLabel=''
            nextLabel=''
          />
        )}
      </div>

      <div className='flex-1 flex justify-end'>
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= pageCount - 1}
          className={`flex items-center gap-2 ${currentPage >= pageCount - 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <NextLabel />
        </button>
      </div>
    </div>
  );
}
