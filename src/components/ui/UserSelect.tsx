import React, { useState, useCallback, useMemo } from 'react';
import { useUsersQuery } from '@/generated/graphql';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/Command';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { ChevronDown, Check as CheckIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';

interface UserSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const UserSelect: React.FC<UserSelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select user...',
  className,
  disabled = false,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const {
    data: usersData,
    loading,
    fetchMore,
  } = useUsersQuery({
    variables: {
      paginationArgs: {
        page: 1,
        limit: 20, // Start with 20 users
      },
      search: searchTerm.trim() || undefined,
    },
    notifyOnNetworkStatusChange: true,
  });

  const users = useMemo(() => {
    return usersData?.users?.items || [];
  }, [usersData]);

  const totalUsers = usersData?.users?.total || 0;
  const currentPage = usersData?.users?.page || 1;

  // Check if we have more data to load
  const hasMoreData = users.length < totalUsers;

  const loadMore = useCallback(async () => {
    if (!hasMoreData || loading) return;

    const nextPage = currentPage + 1;
    await fetchMore({
      variables: {
        paginationArgs: {
          page: nextPage,
          limit: 20,
        },
        search: searchTerm.trim() || undefined,
      },
    });
  }, [fetchMore, hasMoreData, loading, currentPage, searchTerm]);

  // Reset pagination when search term changes
  React.useEffect(() => {
    // Reset to page 1 when search changes
    // This will be handled by the query automatically
  }, [searchTerm]);

  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
  }, []);

  const handleSelectUser = useCallback(
    (userId: string) => {
      onValueChange(userId);
      setIsOpen(false);
    },
    [onValueChange]
  );

  const getSelectedUserName = useCallback(() => {
    if (value === 'All Users') return 'All Users';
    const selectedUser = users.find((user) => user.id === value);
    return selectedUser ? `${selectedUser.firstName} ${selectedUser.lastName}` : placeholder;
  }, [value, users, placeholder]);

  const handleOpenChange = useCallback((open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setSearchTerm('');
    }
  }, []);

  // Infinite scroll hook
  const loadingRef = useInfiniteScroll({
    onLoadMore: loadMore,
    hasMore: hasMoreData,
    loading,
    threshold: 50,
  });

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          disabled={disabled}
          className={cn(
            'w-full justify-between text-[#191E3B] font-normal p-3 border border-gray-200 rounded-md text-sm bg-white',
            {
              'text-muted-foreground': value === 'All Users',
            },
            className
          )}
        >
          {getSelectedUserName()}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[--radix-popover-trigger-width] p-0'>
        <Command>
          <CommandInput
            placeholder='Search users...'
            value={searchTerm}
            onValueChange={handleSearchChange}
          />
          <CommandEmpty>{loading ? 'Loading...' : 'No users found.'}</CommandEmpty>
          <CommandGroup>
            <ScrollArea className='h-64'>
              {/* All Users option */}
              <CommandItem
                value='All Users'
                onSelect={() => handleSelectUser('All Users')}
                className='flex items-center justify-between'
              >
                <span>All Users</span>
                {value === 'All Users' && <CheckIcon className='h-4 w-4 text-primary' />}
              </CommandItem>

              {/* Users list */}
              {users.map((user) => (
                <CommandItem
                  key={user.id}
                  value={user.id}
                  onSelect={() => handleSelectUser(user.id)}
                  className='flex items-center justify-between'
                >
                  <div className='flex flex-col'>
                    <span>
                      {user.firstName} {user.lastName}
                    </span>
                    <span className='text-xs text-gray-500'>{user.email}</span>
                  </div>
                  {value === user.id && <CheckIcon className='h-4 w-4 text-primary' />}
                </CommandItem>
              ))}

              {/* Infinite scroll trigger */}
              {hasMoreData && (
                <div ref={loadingRef} className='p-2 text-center'>
                  {loading && <div className='text-sm text-gray-500'>Loading more users...</div>}
                </div>
              )}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default UserSelect;
