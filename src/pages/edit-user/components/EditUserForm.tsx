import { Button } from '@/components/ui/Button';
import { Form } from '@/components/ui/form/Form';
import { FormInput } from '@/components/ui/form/FormInput';
import { Label } from '@/components/ui/form/Label';
import { Separator } from '@/components/ui/Separator';
import { Skeleton } from '@/components/ui/Skeleton';
import { Mail } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';
import { FormValues } from '../EditUser';
interface EditUserFormProps {
  id: string;
  isLoading: boolean;
  onSubmit: (values: FormValues, formHandler: UseFormReturn<FormValues>) => void;
  formSchema: z.ZodSchema;
  defaultValues: any;
  isUpdatingUser: boolean;
  setIsResetPasswordModalOpen: (open: boolean) => void;
}

const EditUserForm = ({
  id,
  isLoading,
  onSubmit,
  formSchema,
  defaultValues,
  isUpdatingUser,
  setIsResetPasswordModalOpen,
}: EditUserFormProps) => {
  return (
    <Form
      id='info-form'
      key={`${id}-${JSON.stringify(defaultValues)}`}
      mode='onChange'
      onSubmit={(values, formHandler) => onSubmit(values, formHandler as UseFormReturn<FormValues>)}
      schema={formSchema}
      defaultValues={defaultValues}
      showUnsavedChangesConfirmModal
      className='flex flex-col space-y-4'
    >
      <div className='flex w-full justify-between'>
        <h2 className='text-xl font-semibold text-gray-900'>User Info</h2>
        <Button
          type='submit'
          form='info-form'
          disabledOnNotDirty
          disableOnInvalid
          className='hidden sm:block'
          loading={isUpdatingUser}
        >
          Save
        </Button>
      </div>
      <Separator className='hidden sm:block bg-[#EBEDF4]' />
      <div className='flex flex-col sm:flex-row items-start gap-4 sm:gap-8'>
        <div className='w-full sm:w-64'>
          <Label className='font-semibold text-sm hidden sm:block'>Name</Label>
        </div>

        <div className='flex flex-col sm:flex-row gap-4 sm:gap-6 flex-1 max-w-lg w-full'>
          {isLoading ? (
            <>
              <Skeleton className='h-11 w-full sm:flex-1' />
              <Skeleton className='h-11 w-full sm:flex-1' />
            </>
          ) : (
            <>
              <div className='flex flex-col w-full sm:flex-1'>
                <Label className='font-semibold text-sm mb-1 sm:hidden'>First Name</Label>
                <FormInput
                  name='firstName'
                  className='w-full'
                  placeholder='First Name'
                  disabled={true}
                />
              </div>
              <div className='flex flex-col w-full sm:flex-1'>
                <Label className='font-semibold text-sm mb-1 sm:hidden'>Last Name</Label>
                <FormInput
                  name='lastName'
                  className='w-full'
                  placeholder='Last Name'
                  disabled={true}
                />
              </div>
            </>
          )}
        </div>
      </div>

      <Separator className='hidden sm:block bg-[#EBEDF4]' />

      {/* Email Field Section */}
      <div className='flex flex-col sm:flex-row items-start gap-2 sm:gap-8'>
        <div className='w-full sm:w-64'>
          <Label className='font-semibold text-sm text-gray-700'>Email Address</Label>
        </div>
        <div className='w-full sm:max-w-lg flex-1'>
          {isLoading ? (
            <Skeleton className='h-11 w-full' />
          ) : (
            <FormInput
              StartIcon={Mail}
              name='email'
              type='email'
              className='w-full'
              placeholder='Email Address'
            />
          )}
        </div>
      </div>

      <Separator className='hidden sm:block bg-[#EBEDF4]' />

      {/* HOA Association Field Section */}
      <div className='flex flex-col sm:flex-row items-start gap-2 sm:gap-8'>
        <div className='w-full sm:w-64'>
          <Label className='font-semibold text-sm text-gray-700'>HOA Association</Label>
        </div>
        <div className='w-full sm:max-w-lg flex-1'>
          {isLoading ? (
            <Skeleton className='h-11 w-full' />
          ) : (
            <FormInput disabled name='association' placeholder='HOA Association' />
          )}
        </div>
      </div>

      <Separator className='hidden sm:block bg-[#EBEDF4]' />

      {/* password field section */}
      <div className='flex flex-col sm:flex-row items-start gap-2 sm:gap-8'>
        <div className='w-full sm:w-64'>
          <Label className='font-semibold text-sm text-gray-700'>Password</Label>
        </div>
        <div className='w-full sm:max-w-[227px] flex-1'>
          {isLoading ? (
            <Skeleton className='h-11 w-full' />
          ) : (
            <Button
              type='button'
              variant='outline'
              className='w-full'
              onClick={() => setIsResetPasswordModalOpen(true)}
            >
              Send a Reset Password Link
            </Button>
          )}
        </div>
      </div>

      <Separator className='sm:hidden block bg-[#EBEDF4]' />

      <div className='flex sm:hidden mt-4 justify-end'>
        <Button
          type='submit'
          form='info-form'
          disableOnInvalid
          disabledOnNotDirty
          loading={isUpdatingUser}
        >
          Save changes
        </Button>
      </div>
    </Form>
  );
};

export default EditUserForm;
