import { Button } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/Dialog';
import LockIcon from '@/assets/images/lock-icon.svg';

interface Props {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
  title: string;
}
const ResetPasswordModal = ({
  isOpen,
  title,
  onOpenChange,
  onCancel,
  onConfirm,
  isLoading,
}: Props) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent hideClose className='sm:w-[400px] w-[90vw] rounded-md'>
        <DialogHeader className='flex flex-col gap-2 items-center '>
          <div className='flex items-center gap-2'>
            <img src={LockIcon} alt='alert' className='w-12 h-12' />
          </div>
          <DialogTitle className='text-center max-w-full'>{title}</DialogTitle>
          <DialogDescription className='text-center max-w-full break-words'>
            A password reset email will be sent to the homeowner&apos;s email address. They&apos;ll
            be able to set a new password.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className='w-full'>
          <div className='flex sm:flex-row flex-col-reverse flex-1 justify-center gap-2 sm:gap-4 mt-4'>
            <Button className='w-full' variant={'outline'} onClick={onCancel}>
              Cancel
            </Button>
            <Button className='w-full' onClick={onConfirm} disabled={isLoading} loading={isLoading}>
              Reset Password
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ResetPasswordModal;
