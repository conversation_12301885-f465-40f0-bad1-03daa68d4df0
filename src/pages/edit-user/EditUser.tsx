import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { useForgotPasswordMutation, useGetUserByIdQuery } from '@/generated/graphql';
import { useAdminUpdateProfileMutation } from '@/generated/graphql';
import { useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { z } from 'zod';
import { AppRoutePaths } from '@/lib/constants';
import { ArrowLeftIcon } from 'lucide-react';
import { Skeleton } from '@/components/ui/Skeleton';
import ResetPasswordModal from './components/ResetPasswordModal';
import { ApolloError } from '@apollo/client';
import { emailSchema } from '@/lib/schemas';
import EditUserForm from './components/EditUserForm';
import { UseFormReturn } from 'react-hook-form';
import { useToast } from '@/hooks/useToast';

const formSchema = z.object({
  firstName: z
    .string()
    .min(1, { message: 'First name is required' })
    .transform((val) => val.trim()),
  lastName: z
    .string()
    .min(1, { message: 'Last name is required' })
    .transform((val) => val.trim()),
  email: emailSchema,
  association: z.string().optional(),
});

export type FormValues = z.infer<typeof formSchema>;

export const EditUser = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [isResetPasswordModalOpen, setIsResetPasswordModalOpen] = useState(false);

  const [updateUserProfile, { loading: isUpdatingUser }] = useAdminUpdateProfileMutation();

  const [resetPassword, { loading: isResettingPassword }] = useForgotPasswordMutation();

  const {
    data,
    loading: isLoading,
    error,
    refetch,
  } = useGetUserByIdQuery({
    variables: { userByIdId: id ?? '' },
  });

  const userData = useMemo(() => data?.userById, [data]);

  const defaultValues: FormValues = useMemo(() => {
    return {
      firstName: userData?.firstName ?? '',
      lastName: userData?.lastName ?? '',
      email: userData?.email ?? '',
      association: userData?.association?.name ?? '',
    };
  }, [userData]);

  const onSubmit = async (values: FormValues, formHandler: UseFormReturn<FormValues>) => {
    try {
      await updateUserProfile({
        variables: {
          input: {
            userId: id ?? '',
            firstName: values.firstName,
            lastName: values.lastName,
            email: values.email,
          },
        },
      });

      await refetch();

      formHandler.reset();

      toast({
        title: 'User updated successfully',
        variant: 'success',
      });
    } catch (error) {
      if (error instanceof ApolloError) {
        toast({
          title: 'Error updating user',
          description: error.graphQLErrors[0].message,
          variant: 'destructive',
        });
      }
    }
  };

  const onConfirmResetPassword = async () => {
    try {
      await resetPassword({ variables: { input: { email: userData?.email ?? '' } } });
      setIsResetPasswordModalOpen(false);
      toast({
        title: `Password reset email sent to ${userData?.email}. They can now set a new password.`,
        variant: 'success',
      });
    } catch (error) {
      if (error instanceof ApolloError) {
        setIsResetPasswordModalOpen(false);
        toast({
          title: error.graphQLErrors[0].message,
          variant: 'destructive',
        });
      }
    }
  };

  if (error) {
    toast({
      title: 'Error loading user data',
      variant: 'destructive',
    });
    navigate(AppRoutePaths.USERS);
  }

  return (
    <div className='w-full flex flex-col gap-4 sm:gap-8 pb-8 px-4 sm:px-8'>
      <div className='flex flex-col gap-4 items-start px-4 sm:px-8 pt-2'>
        <Button
          variant='ghost'
          className='gap-2 px-0 font-semibold hover:bg-transparent'
          onClick={() => navigate(-1)}
        >
          <ArrowLeftIcon className='w-5 h-5' />
          Back
        </Button>

        <div className='flex w-full flex-col gap-2'>
          {isLoading ? (
            <>
              <Skeleton className='h-[38px] w-[150px]' />
              <Skeleton className='h-[24px] w-[200px]' />
            </>
          ) : (
            <>
              <h1 className='text-[30px] leading-[38px] font-medium text-gray-900'>
                {userData?.firstName} {userData?.lastName}
              </h1>
              <p className='text-base font-normal'>{userData?.email}</p>
            </>
          )}
        </div>
      </div>

      <Card>
        <CardContent className='p-4 sm:p-8'>
          <EditUserForm
            id={id ?? ''}
            isLoading={isLoading}
            onSubmit={onSubmit}
            formSchema={formSchema}
            defaultValues={defaultValues}
            isUpdatingUser={isUpdatingUser}
            setIsResetPasswordModalOpen={setIsResetPasswordModalOpen}
          />
        </CardContent>
      </Card>

      <ResetPasswordModal
        isOpen={isResetPasswordModalOpen}
        onOpenChange={setIsResetPasswordModalOpen}
        onConfirm={onConfirmResetPassword}
        onCancel={() => setIsResetPasswordModalOpen(false)}
        isLoading={isResettingPassword}
        title={`Reset Password for ${userData?.firstName} ${userData?.lastName}`}
      />
    </div>
  );
};
