import LoadingOverlay from '@/components/loading/LoadingOverlay';
import { useUnsubscribeEmailMutation } from '@/generated/graphql';
import { AppRoutePaths } from '@/lib/constants';
import { ArrowLeft } from 'lucide-react';
import { useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';

const UnsubscribeEmailPage = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');

  const [unsubscribeEmail, { loading }] = useUnsubscribeEmailMutation();

  useEffect(() => {
    if (token) {
      unsubscribeEmail({ variables: { input: { token: token as string } } });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading) {
    return <LoadingOverlay isLoading={loading} />;
  }

  return (
    <div className='flex flex-col justify-center min-h-screen p-4 '>
      <div className='text-center'>
        <h2 className='text-2xl font-semibold mb-4 '>You’ve successfully unsubscribed.</h2>
        <p className='text-muted-foreground mb-8 max-w-md inline-block'>
          Thank you for being with us. If you change your mind, you’re always welcome to subscribe
          again anytime.
        </p>
      </div>
      <div className='flex flex-col sm:flex-row justify-center gap-2 '>
        <Link
          to={AppRoutePaths.USERS}
          replace
          className='flex items-center justify-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/80 transition-colors'
        >
          <ArrowLeft size={20} className='stroke-white' />
          Back to Dashboard
        </Link>
      </div>
    </div>
  );
};

export default UnsubscribeEmailPage;
