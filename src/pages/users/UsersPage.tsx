import UserHeader from './components/UserHeader';
import UserTable from './components/UserTable';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { AppRoutePaths } from '@/lib/constants';
import OnboardingModal from './components/OnboardingModal';

const UsersPage = () => {
  const location = useLocation();
  const [showOnboardingModal, setShowOnboardingModal] = useState(false);

  useEffect(() => {
    // Check if user navigated from complete-sign-up route
    // This is to show the onboarding modal for the first time
    if (location.state?.from === AppRoutePaths.COMPLETE_SIGN_UP) {
      setShowOnboardingModal(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className='w-full flex py-4 space-y-4 flex-col flex-1 px-4 sm:px-8'>
      <UserHeader />
      <UserTable />
      <OnboardingModal
        showOnboardingModal={showOnboardingModal}
        setShowOnboardingModal={setShowOnboardingModal}
      />
    </div>
  );
};

export default UsersPage;
