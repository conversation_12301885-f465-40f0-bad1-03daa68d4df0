import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { ChevronDown, ListFilter, X } from 'lucide-react';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import DateRangePicker from '@/components/date-range-picker/DateRangePicker';
import SearchInput from '@/components/ui/search-input/SearchInput';
import { Badge } from '@/components/ui/Badge';
import { DATE_FORMAT } from '@/lib/constants';
import HOASelector from './HOASelector';
import { Association } from '@/generated/graphql';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

interface UserTableFilterProps {
  search: string;
  setSearch: (search: string) => void;
  selectedHOA: Association | null;
  setSelectedHOA: (selectedHOA: Association | null) => void;
  date: DateRange | undefined;
  setDate: (date: DateRange | undefined) => void;
}

const UserTableFilter = ({
  search,
  setSearch,
  selectedHOA,
  setSelectedHOA,
  date,
  setDate,
}: UserTableFilterProps) => {
  const [isOpenFilters, setIsOpenFilters] = useState(false);
  const [isDateOpen, setIsDateOpen] = useState(false);
  const [tempSelectedHOA, setTempSelectedHOA] = useState<Association | null>(selectedHOA);
  const [tempHOASearch, setTempHOASearch] = useState('');
  const [tempDate, setTempDate] = useState<DateRange | undefined>(date);

  const isMobile = useIsMobile();

  const handleApplyFilters = () => {
    setDate(tempDate);
    setSelectedHOA(tempSelectedHOA);
    setIsOpenFilters(false);
  };

  const handleCancelFilters = () => {
    setIsOpenFilters(false);
    setTempSelectedHOA(selectedHOA);
    setTempHOASearch('');
    setTempDate(date);
  };

  const onClearAll = () => {
    setDate(undefined);
    setSelectedHOA(null);
    setTempSelectedHOA(null);
    setTempDate(undefined);
    setTempHOASearch('');
    setIsOpenFilters(false);
  };

  const handleFilterOpenChange = (open: boolean) => {
    setIsOpenFilters(open);
    if (!open) {
      // Reset temporary values when closing without applying
      setTempSelectedHOA(selectedHOA);
      setTempHOASearch('');
      setTempDate(date);
    }
  };

  return (
    <div className='pb-4'>
      <div className='w-full gap-2 flex flex-col sm:flex-row items-center justify-between pb-4  '>
        <div className='flex w-full items-center gap-2'>
          <SearchInput name='search' onChange={(search) => setSearch(search)} value={search} />
        </div>
        <div className='flex w-full sm:w-auto items-center gap-2'>
          <Popover open={isOpenFilters} onOpenChange={handleFilterOpenChange}>
            <PopoverTrigger asChild>
              <Button
                variant='outline'
                className='gap-2 w-full text-gray-700 font-semibold sm:w-auto'
              >
                <ListFilter className='w-5 h-5' />
                Filters
              </Button>
            </PopoverTrigger>
            <PopoverContent align='end' className='sm:w-80  w-[--radix-popover-trigger-width]'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between mb-2'>
                  <label className='text-sm font-medium'>Filter by</label>
                  {(selectedHOA || (date && date.from)) && (
                    <span className='cursor-pointer text-sm font-medium' onClick={onClearAll}>
                      Clear
                    </span>
                  )}
                </div>
                <HOASelector
                  selectedHOA={tempSelectedHOA}
                  setSelectedHOA={setTempSelectedHOA}
                  tempHOASearch={tempHOASearch}
                  setTempHOASearch={setTempHOASearch}
                />

                <Popover open={isDateOpen} onOpenChange={setIsDateOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant='outline'
                      className={cn(
                        'w-full flex justify-between items-center gap-2 font-normal text-[#191E3B]',
                        {
                          'text-muted-foreground': !tempDate?.from,
                        }
                      )}
                    >
                      {tempDate?.from ? (
                        tempDate.to ? (
                          <>
                            {format(tempDate.from, DATE_FORMAT)} -{' '}
                            {format(tempDate.to, DATE_FORMAT)}
                          </>
                        ) : (
                          format(tempDate.from, DATE_FORMAT)
                        )
                      ) : (
                        <span>Date Joined</span>
                      )}
                      <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className='w-[--radix-popover-trigger-width] items-center sm:w-auto p-0 '
                    align='end'
                    sideOffset={isMobile ? -100 : 5}
                  >
                    <DateRangePicker
                      date={tempDate}
                      setDate={setTempDate}
                      onConfirm={(date) => {
                        setTempDate(date);
                        setIsDateOpen(false);
                      }}
                      onCancel={() => {
                        setIsDateOpen(false);
                      }}
                    />
                  </PopoverContent>
                </Popover>

                <div className='flex w-full items-center gap-2'>
                  <Button onClick={handleCancelFilters} variant='outline' className='w-full'>
                    Cancel
                  </Button>
                  <Button
                    disabled={!tempSelectedHOA && !tempDate}
                    onClick={handleApplyFilters}
                    variant='default'
                    className='w-full'
                  >
                    Apply
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <div className='flex flex-col sm:flex-row gap-2'>
        {selectedHOA && (
          <Badge className='text-sm gap-2 hover:bg-secondary text-primary w-fit bg-secondary font-medium'>
            {selectedHOA.name}
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setSelectedHOA(null);
                setTempSelectedHOA(null);
                setTempHOASearch('');
              }}
            />
          </Badge>
        )}
        {date && (date.from || date.to) && (
          <Badge className='text-sm gap-2 text-primary hover:bg-secondary w-fit bg-secondary font-medium'>
            {date.from && format(date.from, DATE_FORMAT)}
            {date.to && ` - ${format(date.to, DATE_FORMAT)}`}
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setDate(undefined);
                setTempDate(undefined);
              }}
            />
          </Badge>
        )}
      </div>
    </div>
  );
};

export default UserTableFilter;
