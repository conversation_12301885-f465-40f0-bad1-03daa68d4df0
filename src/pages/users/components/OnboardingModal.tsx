import { Button } from '@/components/ui/Button';
import { DialogFooter } from '@/components/ui/Dialog';
import { DialogContent } from '@/components/ui/Dialog';
import { Dialog } from '@/components/ui/Dialog';
import OnboardingStep1 from '@/assets/images/onboarding/onboarding-step1.svg';
import OnboardingStep2 from '@/assets/images/onboarding/onboarding-step2.svg';
import { useState } from 'react';

interface OnboardingModalProps {
  showOnboardingModal: boolean;
  setShowOnboardingModal: (showOnboardingModal: boolean) => void;
}

const OnboardingSteps = [
  {
    image: OnboardingStep1,
    title: 'Welcome to your dashboard',
    description: `We're glad to have you onboard. Here are some quick tips to get you up and running.`,
  },
  {
    image: OnboardingStep2,
    title: 'View & Manage Users',
    description:
      'View, search and filter HOA users here. You can send invite emails and reset passwords.',
  },
];

const OnboardingModal = ({ showOnboardingModal, setShowOnboardingModal }: OnboardingModalProps) => {
  const [currentStep, setCurrentStep] = useState(0);

  const handleOnNext = () => {
    if (currentStep === OnboardingSteps.length - 1) {
      setShowOnboardingModal(false);
    } else {
      setCurrentStep(currentStep + 1);
    }
  };

  return (
    <Dialog open={showOnboardingModal} onOpenChange={setShowOnboardingModal}>
      <DialogContent
        onOpenAutoFocus={(e) => e.preventDefault()}
        onInteractOutside={(e) => e.preventDefault()}
        hideClose
        className='sm:w-[400px] max-w-[90vw] rounded-lg p-6'
      >
        <div className='flex flex-col items-center gap-5'>
          <div className='w-full h-[200px] rounded-lg bg-[#E8EBFD] p-4 pb-0'>
            <div className='relative w-full h-full'>
              <img
                src={OnboardingSteps[currentStep].image}
                alt='User'
                className='object-fill sm:object-cover w-full h-full'
              />
            </div>
          </div>

          <div className='text-center'>
            <h2 className='text-lg font-semibold text-gray-900 mb-2'>
              {OnboardingSteps[currentStep].title}
            </h2>
            <p className='text-sm'>{OnboardingSteps[currentStep].description}</p>
          </div>

          {/* Pagination Dots */}
          <div className='flex justify-center space-x-2'>
            {Array.from({ length: OnboardingSteps.length }).map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full  ${currentStep === index ? 'bg-primary' : 'bg-[#E8EBFD]'}`}
              ></div>
            ))}
          </div>
        </div>

        <DialogFooter className='flex justify-between mt-5 gap-3'>
          <Button
            variant='outline'
            className='flex-1 text-base font-semibold  '
            onClick={() => setShowOnboardingModal(false)}
          >
            Skip
          </Button>
          <Button onClick={handleOnNext} className='flex-1 text-base font-semibold'>
            {currentStep === OnboardingSteps.length - 1 ? 'Get Started' : 'Next'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default OnboardingModal;
