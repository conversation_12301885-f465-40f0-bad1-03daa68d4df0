import { Badge } from '@/components/ui/Badge';
import pluralize from 'pluralize';
interface UserTableHeaderProps {
  userCount: number;
}

const UserTableHeader = ({ userCount }: UserTableHeaderProps) => {
  return (
    <div className='flex items-center p-4 gap-2'>
      <span className='text-lg font-medium'>Users</span>
      <Badge variant='default' className='text-primary bg-secondary hover:bg-secondary'>
        {userCount} {pluralize('user', userCount)}
      </Badge>
    </div>
  );
};

export default UserTableHeader;
