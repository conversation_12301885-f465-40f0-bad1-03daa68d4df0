import { Checkbox } from '@/components/ui/Checkbox';
import { Switch } from '@/components/ui/Switch';
import ActionCell from '@/components/ui/table/ActionCell';
import { HeaderColumn } from '@/components/ui/table/HeaderColumn';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { User } from '@/generated/graphql';
import { DATE_FORMAT_MM_DD_YYYY } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { ExternalLink } from 'lucide-react';

export interface UserColumnsProps {
  isLoading: boolean;
  showActions: boolean;
  selectedRowId: string | null;
  actionCellRef?: React.RefObject<HTMLDivElement>;
  onSendSignupEmails: (user: User) => Promise<void>;
  onRemoveUser: (user: User) => void;
  onEditUser: (userId: string) => void;
  onResetPassword: (user: User) => void;
  setSelectedRowId: (id: string | null) => void;
  onOpenUserSalesforce: (salesforceId: string) => void;
  togglingUserId: string | null;
  onToggleUserClubAccess: (userId: string) => void;
}

export function generateUserColumns({
  isLoading,
  showActions,
  actionCellRef,
  selectedRowId,
  togglingUserId,
  onResetPassword,
  onSendSignupEmails,
  onRemoveUser,
  onEditUser,
  setSelectedRowId,
  onOpenUserSalesforce,
  onToggleUserClubAccess,
}: UserColumnsProps) {
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      maxSize: 50,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'ID',
      header: ({ column }) => <HeaderColumn column={column}>User ID</HeaderColumn>,
      cell: ({ row }) => {
        const userId = row.original.id;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-gray-900 font-medium'>{userId}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 100,
      maxSize: 150,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'firstName',
      header: ({ column }) => <HeaderColumn column={column}>First Name</HeaderColumn>,
      cell: ({ row }) => {
        const firstName = row.original.firstName;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-gray-900 font-medium'>{firstName}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 120,
      size: 150,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'lastName',
      header: ({ column }) => <HeaderColumn column={column}>Last Name</HeaderColumn>,
      cell: ({ row }) => {
        const lastName = row.original.lastName;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-gray-900 font-medium'>{lastName}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 120,
      size: 150,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'email',
      header: ({ column }) => <HeaderColumn column={column}>Email</HeaderColumn>,
      cell: ({ row }) => {
        const email = row.original.email;
        const salesforceId = row.original.contact?.salesforceId;

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex items-center justify-between w-full'>
              <span
                className={cn('truncate text-gray-500 font-normal ', showActions && 'max-w-[85%]')}
              >
                {email}
              </span>
              {showActions && (
                <ExternalLink
                  onClick={(e) => {
                    e.stopPropagation();
                    if (salesforceId) {
                      onOpenUserSalesforce(salesforceId);
                    }
                  }}
                  size={20}
                  className='flex-shrink-0 cursor-pointer'
                />
              )}
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 200,
      size: 250,
      maxSize: 250,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'DATE_JOINED',
      header: ({ column }) => <HeaderColumn column={column}>Date Joined</HeaderColumn>,
      cell: ({ row }) => {
        const dateJoined = row.original.dateJoined
          ? format(row.original.dateJoined, DATE_FORMAT_MM_DD_YYYY)
          : '';
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>{dateJoined}</span>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'association',
      header: ({ column }) => <HeaderColumn column={column}>HOA Association</HeaderColumn>,
      cell: ({ row }) => {
        const association = row.original?.association?.name;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate  text-foreground font-normal'>{association}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 150,
      size: 200,
      meta: {
        padding: '20px 16px',
      },
    },
    ...(showActions
      ? [
          {
            accessorKey: 'canUseClubs',
            header: ({ column }: { column: any }) => (
              <HeaderColumn column={column}>Club Access</HeaderColumn>
            ),
            cell: ({ row }: { row: any }) => {
              return (
                <SkeletonCell isLoading={isLoading} skeletonCount={1}>
                  <div className='flex items-center'>
                    <Switch
                      checked={row.original.canUseClubs || false}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                      disabled={togglingUserId === row.original.id}
                      onCheckedChange={() => {
                        onToggleUserClubAccess(row.original.id);
                      }}
                    />
                  </div>
                </SkeletonCell>
              );
            },
            enableSorting: false,
            size: 150,
          } as ColumnDef<User>,
        ]
      : []),
    ...(showActions
      ? [
          {
            accessorKey: 'actions',
            header: '',
            cell: ({ row }) => {
              const user = row.original;
              const userId = user.id;
              const dateJoined = user.dateJoined;
              const actions = [
                {
                  label: 'Edit User Info',
                  onClick: () => {
                    onEditUser(userId);
                  },
                },
                {
                  label: 'Reset Password',
                  onClick: () => {
                    onResetPassword(user);
                  },
                },
                {
                  label: 'Remove User',
                  onClick: () => {
                    onRemoveUser(user);
                  },
                },
              ];

              if (!dateJoined) {
                actions.unshift({
                  label: 'Send Signup Email',
                  onClick: () => {
                    onSendSignupEmails(user);
                  },
                });
              }
              return (
                <SkeletonCell isLoading={isLoading} skeletonCount={1}>
                  <ActionCell
                    ref={actionCellRef}
                    isSelected={selectedRowId === userId}
                    setSelectedRowId={setSelectedRowId}
                    actions={actions}
                  />
                </SkeletonCell>
              );
            },
            size: 50,
            meta: {
              padding: '20px 16px',
            },
          } as ColumnDef<User>,
        ]
      : []),
  ];

  return columns;
}

export default generateUserColumns;
