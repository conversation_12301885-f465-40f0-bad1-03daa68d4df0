query users($paginationArgs: PaginationArgs!, $sort: UserSortInput, $filter: UserFilterInput, $search: String) {
  users(paginationArgs: $paginationArgs, sort: $sort, filter: $filter, search: $search) {
    items {
      id
      firstName
      lastName
      email
      phone
      dob
      association {
        id
        name
      }
      dateJoined
      contact {
        salesforceId
        id
      }
      role
      isDeleted
      canUseClubs
    }
    total
    page
    limit
  }
}

query getUserById($userByIdId: ID!) {
  userById(id: $userByIdId) {
    id
    firstName
    lastName
    email
    phone
    dob
    dateJoined
    association {
      id
      name
    }
    role
    isDeleted
  }
}

query associations($paginationArgs: PaginationArgs, $search: String) {
  associations(paginationArgs: $paginationArgs, search: $search) {
    items {
      id
      name
    }
    limit
    page
    total
  }
}

mutation adminSendSignupEmails($input: SendSignupEmailsInput!) {
  adminSendSignupEmails(input: $input) {
    message
  }
}

mutation adminRemoveUser($input: AdminRemoveUserInput!) {
  adminRemoveUser(input: $input) {
    message
  }
}

mutation toggleUserClubFeature($userId: ID!) {
  toggleUserClubFeature(userId: $userId) {
    id
    firstName
    lastName
    email
    phone
    dob
    birthdayMonth
    birthdayDay
    dateJoined
    role
    isDeleted
    canUseClubs
  }
}