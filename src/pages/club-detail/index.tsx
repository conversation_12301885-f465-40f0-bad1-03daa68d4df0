import { useCallback } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import ClubDetailHeader from './components/ClubDetailHeader';
import ClubDetailTabs from './components/ClubDetailTabs';
import MembersTab from './components/members/MembersTab';
import PostsTab from './components/posts/PostsTab';
import EventsTab from './components/events/EventsTab';

type TabType = 'members' | 'posts' | 'events';

const ClubDetail = () => {
  const { clubId, associationId } = useParams<{ clubId: string; associationId: string }>();
  const [searchParams, setSearchParams] = useSearchParams();

  const activeTab = (searchParams.get('tab') as TabType) || 'members';

  const handleTabChange = useCallback(
    (newTab: TabType) => {
      if (newTab === activeTab) return;
      setSearchParams({ tab: newTab });
    },
    [activeTab, setSearchParams]
  );

  if (!clubId || !associationId) {
    return <div>Club not found</div>;
  }

  return (
    <div className='w-full flex py-4 space-y-4 flex-col flex-1 px-4 sm:px-8'>
      <ClubDetailHeader clubId={clubId} />
      <ClubDetailTabs activeTab={activeTab} setActiveTab={handleTabChange} />

      {/* Tab Content */}
      {activeTab === 'members' && <MembersTab clubId={clubId} />}
      {activeTab === 'posts' && <PostsTab clubId={clubId} />}
      {activeTab === 'events' && <EventsTab clubId={clubId} />}
    </div>
  );
};

export default ClubDetail;
