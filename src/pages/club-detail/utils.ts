import { ClubCategoryEnum } from '@/generated/graphql';
import { DATE_FORMAT } from '@/lib/constants';
import { format, isSameDay } from 'date-fns';

export const getDisplayName = (firstName?: string | null, lastName?: string | null) => {
  if (firstName && lastName) return `${firstName} ${lastName}`;
  if (firstName) return firstName;
  if (lastName) return lastName;
  return '';
};

export const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes > 1440) {
    return format(date, DATE_FORMAT);
  }

  return 'Today';
};

export const formatEventDateTime = (startTime?: string, endTime?: string) => {
  if (!startTime || !endTime) return '';

  const startDate = new Date(startTime);
  const endDate = new Date(endTime);

  const startDateStr = format(startDate, DATE_FORMAT);
  const endDateStr = format(endDate, DATE_FORMAT);

  const startTimeStr = format(startDate, 'h:mm a');
  const endTimeStr = format(endDate, 'h:mm a');

  // Check if start and end dates are the same day
  if (isSameDay(startDate, endDate)) {
    return `${startDateStr} • ${startTimeStr} - ${endTimeStr}`;
  }

  // Different dates - show full format for both
  return `${startDateStr} • ${startTimeStr} - ${endDateStr} • ${endTimeStr}`;
};

export const getClubCategoryLabel = (category: ClubCategoryEnum) => {
  switch (category) {
    case ClubCategoryEnum.Creative:
      return 'Creative';
    case ClubCategoryEnum.FitnessOutdoor:
      return 'Fitness & Outdoor';
    case ClubCategoryEnum.FoodDrink:
      return 'Food & Drink';
    case ClubCategoryEnum.Hobbies:
      return 'Hobbies';
    case ClubCategoryEnum.SocialFamily:
      return 'Social & Family';
    default:
      return '';
  }
};
