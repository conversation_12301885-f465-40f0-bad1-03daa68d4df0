import { useState, useCallback } from 'react';
import { AdminClubMembership, useRemoveClubMemberMutation } from '@/generated/graphql';
import { toast } from '@/hooks/useToast';

export const useMemberActions = (clubId: string) => {
  const [selectedMember, setSelectedMember] = useState<AdminClubMembership | null>(null);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);

  const [removeClubMemberMutation, { loading: isRemovingMember }] = useRemoveClubMemberMutation({
    refetchQueries: ['AdminClubMembers'],
    onCompleted: () => {
      toast({
        description: 'Member has been successfully removed from the club.',
        variant: 'success',
      });
      handleCloseRemoveMemberModal();
    },
    onError: (error) => {
      toast({
        description: error.message || 'Failed to remove member from club.',
        variant: 'destructive',
      });
    },
  });

  const handleOpenRemoveMemberModal = useCallback((member: AdminClubMembership) => {
    setSelectedMember(member);
    setIsRemoveModalOpen(true);
  }, []);

  const handleCloseRemoveMemberModal = useCallback(() => {
    setSelectedMember(null);
    setIsRemoveModalOpen(false);
  }, []);

  const handleRemoveMember = useCallback(async () => {
    if (!selectedMember?.id || !clubId) {
      toast({
        title: 'Error',
        description: 'Missing required information to remove member.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await removeClubMemberMutation({
        variables: {
          input: {
            clubTemplateId: clubId,
            membershipId: selectedMember.id,
          },
        },
      });
    } catch (error) {
      // Error is already handled in onError callback
      console.error('Failed to remove member:', error);
    }
  }, [selectedMember, clubId, removeClubMemberMutation]);

  return {
    selectedMember,
    isRemoveModalOpen,
    isRemovingMember,
    handleOpenRemoveMemberModal,
    handleCloseRemoveMemberModal,
    handleRemoveMember,
  };
};
