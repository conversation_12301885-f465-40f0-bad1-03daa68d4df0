import { useState, useMemo } from 'react';
import { ColumnFiltersState, RowSelectionState } from '@tanstack/react-table';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { DATE_FORMAT_API } from '@/lib/constants';
import {
  ClubMembersOrderByEnum,
  OrderDirection,
  useAdminClubMembersQuery,
} from '@/generated/graphql';
import usePagination from '@/hooks/usePagination';
import useSort from '@/hooks/useSort';
import { useSearchQuery } from '@/hooks/useSearchQuery';

export const useMemberFilters = (clubId: string) => {
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [dateJoined, setDateJoined] = useState<DateRange | undefined>(undefined);

  const { pagination, setPagination } = usePagination();
  const { sorting, setSorting } = useSort();
  const { searchTemp, setSearchTemp } = useSearchQuery('club-members');

  const { data: membersData, loading: isLoadingMembers } = useAdminClubMembersQuery({
    variables: {
      clubId: clubId,
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        search: searchTemp.trim() || undefined,
        dateJoinedTo: dateJoined?.to ? format(dateJoined.to, DATE_FORMAT_API) : undefined,
        dateJoinedFrom: dateJoined?.from ? format(dateJoined.from, DATE_FORMAT_API) : undefined,
      },
      orderBy:
        sorting.length > 0
          ? {
              field: sorting[0]?.id as ClubMembersOrderByEnum,
              direction: sorting[0]?.desc ? OrderDirection.Desc : OrderDirection.Asc,
            }
          : undefined,
    },
  });

  const members = useMemo(() => {
    if (isLoadingMembers) {
      return Array(10).fill({});
    }
    return membersData?.adminClubMembers?.items ?? [];
  }, [isLoadingMembers, membersData]);

  const totalMembers = useMemo(() => membersData?.adminClubMembers?.total ?? 0, [membersData]);

  return {
    // State
    selectedRows,
    setSelectedRows,
    selectedRowId,
    setSelectedRowId,
    columnFilters,
    setColumnFilters,
    dateJoined,
    setDateJoined,

    // Hooks
    pagination,
    setPagination,
    sorting,
    setSorting,
    searchTemp,
    setSearchTemp,

    // Data
    members,
    totalMembers,
    isLoadingMembers,
  };
};
