import { AdminClubMembership } from '@/generated/graphql';
import RemoveUserModal from '@/components/modals/remove-modal/RemoveModal';

interface MemberActions {
  selectedMember: AdminClubMembership | null;
  isRemoveModalOpen: boolean;
  isRemovingMember: boolean;
  handleOpenRemoveMemberModal: (member: AdminClubMembership) => void;
  handleCloseRemoveMemberModal: () => void;
  handleRemoveMember: () => Promise<void>;
}

interface MemberModalsProps {
  memberActions: MemberActions;
}

export const MemberModals = ({ memberActions }: MemberModalsProps) => {
  return (
    <>
      {/* Remove Member Modal */}
      <RemoveUserModal
        isOpen={memberActions.isRemoveModalOpen}
        onOpenChange={memberActions.handleCloseRemoveMemberModal}
        onCancel={memberActions.handleCloseRemoveMemberModal}
        onConfirm={memberActions.handleRemoveMember}
        isLoading={memberActions.isRemovingMember}
        title='Remove User'
        confirmText='Remove'
        description={`Are you sure you want to remove this user
         from this club? Once removed, they will no longer be able to join or post in this club.`}
      />
    </>
  );
};
