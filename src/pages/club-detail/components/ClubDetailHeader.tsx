import { useAdminClubDetailByIdQuery, UserRole } from '@/generated/graphql';
import { Button } from '@/components/ui/Button';
import { Edit, Calendar, User } from 'lucide-react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/Badge';
import { useNavigate } from 'react-router-dom';
import { getClubCategoryLabel } from '../utils';
import { useAuthContext } from '@/pages/auth/AuthContext';

interface ClubDetailHeaderProps {
  clubId: string;
}

const ClubDetailHeader = ({ clubId }: ClubDetailHeaderProps) => {
  const { data: clubData } = useAdminClubDetailByIdQuery({
    variables: { adminClubByIdId: clubId },
  });
  const { userData } = useAuthContext();

  const navigate = useNavigate();

  const club = clubData?.adminClubById;
  const clubTemplate = club?.clubTemplate;

  const handleEditClubDetails = () => {
    navigate(`/clubs/${clubData?.adminClubById?.clubTemplate?.id}`);
  };

  if (!club || !clubTemplate) {
    return <div>Club not found</div>;
  }

  return (
    <div className='w-full'>
      {/* Club Section with Image on Left and Info on Right */}
      <div className='w-full bg-white overflow-hidden'>
        <div className='flex sm:flex-row flex-col'>
          {/* Club Image - Left Side */}
          <div className='sm:w-[600px] h-[300px] w-full overflow-hidden rounded-2xl bg-gradient-to-r from-blue-400 to-purple-500'>
            {clubTemplate.img?.url ? (
              <img
                src={clubTemplate.img.url}
                alt={clubTemplate.name}
                className='w-full h-full object-cover'
              />
            ) : (
              <div className='w-full h-full flex items-center justify-center text-white text-2xl font-bold'>
                {clubTemplate.name}
              </div>
            )}
          </div>

          {/* Club Information - Right Side */}
          <div className='flex-1 sm:px-8 mt-4 sm:mt-0 flex flex-col justify-between'>
            {/* Breadcrumb and Edit Button */}
            <div>
              <div className='flex items-center justify-between mb-4'>
                <Badge variant='secondary' className='text-primary text-sm font-medium'>
                  {clubTemplate.category ? getClubCategoryLabel(clubTemplate.category) : ''}
                </Badge>
                {userData?.role === UserRole.Admin && (
                  <Button
                    variant='ghost'
                    size='sm'
                    className='text-primary text-sm bg-secondary font-semibold'
                    onClick={handleEditClubDetails}
                  >
                    <Edit className='w-4 h-4 mr-2' />
                    Edit Club Details
                  </Button>
                )}
              </div>
              {/* Club Title */}
              <h1 className='text-3xl font-medium text-gray-900 mb-2'>{clubTemplate.name}</h1>

              {/* Subtitle */}
              <p className='text-base text-gray-600 mb-6'>
                {clubTemplate.description || 'Neighborhood walks'}
              </p>

              {/* Description */}
              <p className='text-gray-700 mb-8 leading-relaxed'>
                {clubTemplate.about ||
                  "Our Tennis Club welcomes players of all skill levels, whether you're looking for recreational fun or competitive matches. From casual games to organized tournaments, we offer something for everyone who enjoys the sport."}
              </p>
            </div>

            {/* Stats */}
            <div className='flex items-center gap-8'>
              <div className='flex items-center gap-2 text-gray-700'>
                <User className='w-5 h-5 text-primary' />
                <span className='text-base'>{club.memberCount || 0}</span>
              </div>
              <div className='flex items-center gap-2 text-gray-700'>
                <Calendar className='w-5 h-5 text-primary' />
                <span className='text-base'>
                  {club.activatedAt ? format(new Date(club.activatedAt), 'MMMM yyyy') : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClubDetailHeader;
