import { cn } from '@/lib/utils';

type TabType = 'members' | 'posts' | 'events';

interface ClubDetailTabsProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
}

const ClubDetailTabs = ({ activeTab, setActiveTab }: ClubDetailTabsProps) => {
  const tabs: { id: TabType; label: string }[] = [
    { id: 'members', label: 'Members' },
    { id: 'posts', label: 'Posts' },
    { id: 'events', label: 'Events' },
  ];

  return (
    <div className='w-full bg-white rounded-lg'>
      <div className='border-b border-gray-200'>
        <nav className='flex' aria-label='Tabs'>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={cn(
                'p-4 border-b-2 font-semibold text-base whitespace-nowrap  border-transparent  hover:text-gray-700',
                activeTab === tab.id && 'border-primary text-primary'
              )}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default ClubDetailTabs;
