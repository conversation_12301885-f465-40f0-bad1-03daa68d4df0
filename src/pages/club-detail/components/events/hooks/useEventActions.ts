import {
  AdminClubEventsDocument,
  useAdminDeleteClubEventMutation,
  useAdminUnflagReportsByEventIdMutation,
  useToggleUserClubFeatureMutation,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';
import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError } from '@apollo/client';
import { useCallback } from 'react';
import { PaginationState } from '@tanstack/react-table';
import { useSearchParams } from 'react-router-dom';

interface UseEventActionsProps {
  pagination: PaginationState;
  totalItems: number;
  currentItemsCount: number;
}

export const useEventActions = ({
  pagination,
  totalItems,
  currentItemsCount,
}: UseEventActionsProps) => {
  const [removeEvent, { loading: isRemovingEvent }] = useAdminDeleteClubEventMutation();
  const [disableClubAccess, { loading: isDisablingClubAccess }] =
    useToggleUserClubFeatureMutation();
  const [unflagEvent, { loading: isUnflaggingEvent }] = useAdminUnflagReportsByEventIdMutation();
  const [searchParams, setSearchParams] = useSearchParams();

  const { toast } = useToast();

  // Handle API errors uniformly
  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  // Check if we need to adjust pagination after deletion
  const shouldAdjustPagination = useCallback(() => {
    // Only adjust if:
    // 1. Not on page 1
    // 2. Current page has only 1 item (will become empty after deletion)
    // 3. We're on the last page
    const currentPage = pagination.pageIndex;
    const pageSize = pagination.pageSize;
    const totalPages = Math.ceil(totalItems / pageSize);

    return currentPage > 1 && currentItemsCount === 1 && currentPage === totalPages;
  }, [pagination.pageIndex, pagination.pageSize, totalItems, currentItemsCount]);

  // Navigate to previous page using URL navigation
  const navigateToPreviousPage = useCallback(() => {
    if (shouldAdjustPagination()) {
      const newParams = new URLSearchParams(searchParams);
      const newPageIndex = pagination.pageIndex - 1;
      newParams.set('page', newPageIndex.toString());

      // Use replace to avoid creating unnecessary history entries
      setSearchParams(newParams, { replace: true });
    }
  }, [shouldAdjustPagination, searchParams, setSearchParams, pagination.pageIndex]);

  const handleRemoveEvent = useCallback(
    async (eventId: string) => {
      try {
        // Check if we need to adjust pagination before deletion
        const needsAdjustment = shouldAdjustPagination();

        await removeEvent({
          variables: { clubEventId: eventId },
          refetchQueries: [AdminClubEventsDocument],
        });

        // Navigate to previous page after successful deletion if needed
        if (needsAdjustment) {
          navigateToPreviousPage();
        }

        toast({
          variant: 'success',
          title: 'Event deleted successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while removing event');
      }
    },
    [handleApiError, removeEvent, toast, shouldAdjustPagination, navigateToPreviousPage]
  );

  const handleDisableClubAccess = useCallback(
    async (userId: string) => {
      try {
        await disableClubAccess({ variables: { userId } });
        toast({
          variant: 'success',
          title: 'Club access disabled successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while disabling club access');
      }
    },
    [disableClubAccess, handleApiError, toast]
  );

  const handleUnflagEvent = useCallback(
    async (eventId: string) => {
      try {
        await unflagEvent({
          variables: { eventId },
          refetchQueries: [AdminClubEventsDocument],
        });
        toast({
          variant: 'success',
          title: 'Event unflagged successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while unflagging event');
      }
    },
    [handleApiError, toast, unflagEvent]
  );

  return {
    isRemovingEvent,
    isDisablingClubAccess,
    isUnflaggingEvent,
    handleRemoveEvent,
    handleDisableClubAccess,
    handleUnflagEvent,
  };
};
