import { useCallback, useMemo, useEffect, useState } from 'react';
import { useAdminClubEventsQuery, AdminEventStatus, UserRole } from '@/generated/graphql';
import usePagination from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';

import { TablePagination } from '@/components/ui/table/TablePagination';
import EventTableItem from './EventTableItem';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';
import { useEventFilters } from './hooks/useEventFilters';
import EventTableFilter from './EventTableFilter';
import { useEventActions } from './hooks/useEventActions';
import { DATE_FORMAT_API } from '@/lib/constants';
import { format } from 'date-fns';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { DeleteEventModal } from './DeleteEventModal';

interface EventsTabProps {
  clubId: string;
}

const EventsTab = ({ clubId }: EventsTabProps) => {
  const { searchTemp, setSearchTemp } = useSearchQuery('club-events');
  const { pagination, setPagination } = usePagination();
  const eventFilters = useEventFilters();
  const { userData } = useAuthContext();

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [eventToDelete, setEventToDelete] = useState<string | null>(null);

  useEffect(() => {
    setPagination({ pageIndex: 1, pageSize: 10 });
  }, [
    searchTemp,
    eventFilters.selectedUserId,
    eventFilters.dateRange,
    eventFilters.showFlaggedOnly,
    eventFilters.status,
    setPagination,
  ]);

  const { data: eventsData, loading: isLoadingEvents } = useAdminClubEventsQuery({
    variables: {
      clubId: clubId,
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        search: searchTemp,
        userId: eventFilters.selectedUserId || undefined,
        eventsDateFrom: eventFilters.dateRange?.from
          ? format(eventFilters.dateRange.from, DATE_FORMAT_API)
          : undefined,
        eventsDateTo: eventFilters.dateRange?.to
          ? format(eventFilters.dateRange.to, DATE_FORMAT_API)
          : undefined,
        onlyFlagged: eventFilters.showFlaggedOnly || undefined,
        status: (eventFilters.status as AdminEventStatus) || undefined,
      },
    },
  });

  const events = useMemo(() => {
    if (isLoadingEvents) {
      return Array(10).fill({});
    }
    return eventsData?.adminClubEvents?.items ?? [];
  }, [isLoadingEvents, eventsData?.adminClubEvents?.items]);

  const totalEvents = useMemo(() => eventsData?.adminClubEvents?.total ?? 0, [eventsData]);

  const { handleRemoveEvent, handleDisableClubAccess, handleUnflagEvent, isRemovingEvent } =
    useEventActions({
      pagination,
      totalItems: totalEvents,
      currentItemsCount: events.length,
    });

  // Delete modal handlers
  const handleOpenDeleteModal = useCallback((eventId: string) => {
    setEventToDelete(eventId);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setEventToDelete(null);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (eventToDelete) {
      await handleRemoveEvent(eventToDelete);
      handleCloseDeleteModal();
    }
  }, [eventToDelete, handleRemoveEvent, handleCloseDeleteModal]);

  return (
    <div className='w-full flex flex-col py-6 bg-white rounded-lg'>
      {/* Search and Filters */}
      <EventTableFilter
        clubId={clubId}
        search={searchTemp}
        setSearch={setSearchTemp}
        selectedUserId={eventFilters.selectedUserId}
        setSelectedUserId={eventFilters.setSelectedUserId}
        selectedUserName={eventFilters.selectedUserName}
        setSelectedUserName={eventFilters.setSelectedUserName}
        dateRange={eventFilters.dateRange}
        setDateRange={eventFilters.setDateRange}
        showFlaggedOnly={eventFilters.showFlaggedOnly}
        setShowFlaggedOnly={eventFilters.setShowFlaggedOnly}
        status={eventFilters.status}
        setStatus={eventFilters.setStatus}
      />

      <div className='border rounded-xl overflow-hidden'>
        <TableHeaderCount title='Events' total={totalEvents} />
        {/* Posts List */}
        <div className='flex flex-col gap-4 px-4'>
          {events.map((event) => {
            return (
              <EventTableItem
                isLoading={isLoadingEvents}
                key={event.id}
                event={event}
                showActions={userData?.role === UserRole.Admin}
                onRemoveEvent={handleOpenDeleteModal}
                onDisableClubAccess={handleDisableClubAccess}
                onUnflagEvent={handleUnflagEvent}
              />
            );
          })}
        </div>

        {totalEvents === 0 && (
          <div className='flex flex-col items-center justify-center h-full'>
            <p className='text-sm py-2'>No Results.</p>
          </div>
        )}

        {/* Pagination */}
        {totalEvents > 0 && (
          <div className='pt-4'>
            <TablePagination
              pageCount={Math.ceil(totalEvents / pagination.pageSize)}
              currentPage={pagination.pageIndex - 1}
              onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
            />
          </div>
        )}
      </div>

      {/* Delete Event Modal */}
      <DeleteEventModal
        isOpen={isDeleteModalOpen}
        isDeleting={isRemovingEvent}
        onOpenChange={setIsDeleteModalOpen}
        onCancel={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
      />
    </div>
  );
};

export default EventsTab;
