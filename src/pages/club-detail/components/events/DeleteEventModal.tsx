import RemoveModal from '@/components/modals/remove-modal/RemoveModal';

interface DeleteEventModalProps {
  isOpen: boolean;
  isDeleting: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onConfirm: () => Promise<void>;
}

export const DeleteEventModal = ({
  isOpen,
  isDeleting,
  onOpenChange,
  onCancel,
  onConfirm,
}: DeleteEventModalProps) => {
  return (
    <RemoveModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      isLoading={isDeleting}
      title='Delete Event'
      confirmText='Delete'
      description='Are you sure you want to delete this event? This action cannot be undone.'
    />
  );
};
