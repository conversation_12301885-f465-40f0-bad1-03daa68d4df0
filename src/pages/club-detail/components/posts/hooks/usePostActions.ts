import {
  AdminClubPostsDocument,
  useAdminRemoveClubPostByIdMutation,
  useAdminUnflagReportsByPostIdMutation,
  useToggleUserClubFeatureMutation,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';

import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError } from '@apollo/client';
import { useCallback } from 'react';

export const usePostActions = () => {
  const [removePost, { loading: isRemovingPost }] = useAdminRemoveClubPostByIdMutation();
  const [disableClubAccess, { loading: isDisablingClubAccess }] =
    useToggleUserClubFeatureMutation();
  const [unflagPost, { loading: isUnflaggingPost }] = useAdminUnflagReportsByPostIdMutation();

  const { toast } = useToast();

  // Handle API errors uniformly
  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  const handleRemovePost = useCallback(
    async (postId: string) => {
      try {
        await removePost({ variables: { postId }, refetchQueries: [AdminClubPostsDocument] });
        toast({
          variant: 'success',
          title: 'Post deleted successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while removing post');
      }
    },
    [handleApiError, removePost, toast]
  );

  const handleDisableClubAccess = useCallback(
    async (userId: string) => {
      try {
        await disableClubAccess({ variables: { userId } });
        toast({
          variant: 'success',
          title: 'Club access disabled successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while disabling club access');
      }
    },
    [disableClubAccess, handleApiError, toast]
  );

  const handleUnflagPost = useCallback(
    async (postId: string) => {
      try {
        await unflagPost({ variables: { postId }, refetchQueries: [AdminClubPostsDocument] });
        toast({
          variant: 'success',
          title: 'Post unflagged successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while unflagging post');
      }
    },
    [handleApiError, toast, unflagPost]
  );

  return {
    isRemovingPost,
    isDisablingClubAccess,
    isUnflaggingPost,
    handleRemovePost,
    handleDisableClubAccess,
    handleUnflagPost,
  };
};
