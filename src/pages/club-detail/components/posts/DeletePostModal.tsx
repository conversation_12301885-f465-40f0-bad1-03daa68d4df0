import RemoveModal from '@/components/modals/remove-modal/RemoveModal';

interface DeletePostModalProps {
  isOpen: boolean;
  isDeleting: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onConfirm: () => Promise<void>;
}

export const DeletePostModal = ({
  isOpen,
  isDeleting,
  onOpenChange,
  onCancel,
  onConfirm,
}: DeletePostModalProps) => {
  return (
    <RemoveModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      isLoading={isDeleting}
      title='Delete Post'
      confirmText='Delete'
      description='Are you sure you want to delete this post? This action cannot be undone.'
    />
  );
};
