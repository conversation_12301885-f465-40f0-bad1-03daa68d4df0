import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

const NotFoundPage = () => {
  return (
    <div className='flex flex-col justify-center min-h-screen p-4 '>
      <div className='text-center'>
        <h1 className='text-6xl font-bold text-primary'> 404</h1>
        <h2 className='text-2xl font-semibold mb-4 '> Page not found</h2>
      </div>
      <div className='flex flex-col sm:flex-row justify-center gap-2 '>
        <Link
          to='/'
          className='flex items-center justify-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/80 transition-colors'
        >
          <ArrowLeft size={20} className='stroke-white' />
          Back to Dashboard
        </Link>
      </div>
    </div>
  );
};

export default NotFoundPage;
