query AdminAssociations($paginationArgs: PaginationArgs, $filter: AdminAssociationsFilterInput, $orderBy: AdminAssociationsOrderInput) {
  adminAssociations(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      activeClubCount
      canUseClubs
      clubMemberCount
      createdAt
      id
      memberCount
      name
      updatedAt
    }
    total
    page
    limit
  }
}

query AdminAssociationById($adminAssociationByIdId: ID!) {
  adminAssociationById(id: $adminAssociationByIdId) {
    clubMemberCount
    id
    name
    memberCount
    createdAt
    updatedAt
  }
}

mutation toggleAssociationClubFeature($associationId: ID!) {
  toggleAssociationClubFeature(associationId: $associationId) {
    id
    name
    canUseClubs
  }
}