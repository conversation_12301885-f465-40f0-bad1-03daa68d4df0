import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { AdminAssociation } from '@/generated/graphql';
import RemoveUserModal from '@/components/modals/remove-modal/RemoveModal';

interface AssociationActions {
  selectedAssociation: AdminAssociation | null;
  isCreateModalOpen: boolean;
  isEditModalOpen: boolean;
  isDeleteModalOpen: boolean;
  handleOpenCreateAssociationModal: () => void;
  handleCloseCreateAssociationModal: () => void;
  handleOpenEditAssociationModal: (association: AdminAssociation) => void;
  handleCloseEditAssociationModal: () => void;
  handleOpenDeleteAssociationModal: (association: AdminAssociation) => void;
  handleCloseDeleteAssociationModal: () => void;
}

interface AssociationModalsProps {
  associationActions: AssociationActions;
  selectedAssociation: AdminAssociation | null;
}

export const AssociationModals = ({
  associationActions,
  selectedAssociation,
}: AssociationModalsProps) => {
  return (
    <>
      {/* Create Association Modal */}
      <Dialog
        open={associationActions.isCreateModalOpen}
        onOpenChange={associationActions.handleCloseCreateAssociationModal}
      >
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>Create Association</DialogTitle>
          </DialogHeader>
          <div className='flex flex-col gap-4'>
            <p className='text-sm text-gray-600'>
              Create a new association. This feature is coming soon.
            </p>
            <div className='flex justify-end gap-2'>
              <Button
                variant='outline'
                onClick={associationActions.handleCloseCreateAssociationModal}
              >
                Cancel
              </Button>
              <Button disabled>Create</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Association Modal */}
      <Dialog
        open={associationActions.isEditModalOpen}
        onOpenChange={associationActions.handleCloseEditAssociationModal}
      >
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>Edit Association</DialogTitle>
          </DialogHeader>
          <div className='flex flex-col gap-4'>
            <p className='text-sm text-gray-600'>
              Edit association: <strong>{selectedAssociation?.name}</strong>
            </p>
            <p className='text-sm text-gray-600'>This feature is coming soon.</p>
            <div className='flex justify-end gap-2'>
              <Button
                variant='outline'
                onClick={associationActions.handleCloseEditAssociationModal}
              >
                Cancel
              </Button>
              <Button disabled>Save Changes</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Association Modal */}
      <RemoveUserModal
        isOpen={associationActions.isDeleteModalOpen}
        onOpenChange={associationActions.handleCloseDeleteAssociationModal}
        onCancel={associationActions.handleCloseDeleteAssociationModal}
        onConfirm={() => {
          // Handle delete confirmation - will be implemented later
          associationActions.handleCloseDeleteAssociationModal();
        }}
        isLoading={false}
        title='Delete Association'
        description={`Are you sure you want to delete association: ${selectedAssociation?.name}? This action cannot be undone.`}
      />
    </>
  );
};
