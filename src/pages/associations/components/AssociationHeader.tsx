import { useAuthContext } from '@/pages/auth/AuthContext';

const AssociationHeader = () => {
  const { userData } = useAuthContext();

  return (
    <div className='w-full flex items-start sm:items-center gap-2 flex-col sm:flex-row justify-between'>
      <h2 className='sm:text-3xl text-2xl font-medium text-gray-900 sm:py-4'>
        Welcome back,{' '}
        <span className='text-primary'>
          {userData?.firstName} {userData?.lastName}
        </span>
      </h2>
    </div>
  );
};

export default AssociationHeader;
