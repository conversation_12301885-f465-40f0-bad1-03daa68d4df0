import { useState, useCallback } from 'react';
import { AdminAssociation } from '@/generated/graphql';

export const useAssociationActions = () => {
  const [selectedAssociation, setSelectedAssociation] = useState<AdminAssociation | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleOpenCreateAssociationModal = useCallback(() => {
    setIsCreateModalOpen(true);
  }, []);

  const handleCloseCreateAssociationModal = useCallback(() => {
    setIsCreateModalOpen(false);
  }, []);

  const handleOpenEditAssociationModal = useCallback((association: AdminAssociation) => {
    setSelectedAssociation(association);
    setIsEditModalOpen(true);
  }, []);

  const handleCloseEditAssociationModal = useCallback(() => {
    setSelectedAssociation(null);
    setIsEditModalOpen(false);
  }, []);

  return {
    selectedAssociation,
    isCreateModalOpen,
    isEditModalOpen,
    handleOpenCreateAssociationModal,
    handleCloseCreateAssociationModal,
    handleOpenEditAssociationModal,
    handleCloseEditAssociationModal,
  };
};
