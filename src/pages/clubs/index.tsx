import ClubHeader from './components/club-tab/ClubHeader';
import ClubTable from './components/club-tab/ClubTable';
import RequestsTab from './components/club-requests-tab/ClubRequestTable';
import { useCallback } from 'react';
import { Button } from '@/components/ui/Button';
import { Plus } from 'lucide-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { AppRoutePaths } from '@/lib/constants';
import { useAuthContext } from '../auth/AuthContext';
import { UserRole } from '@/generated/graphql';

const Clubs = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTab = (searchParams.get('tab') as 'clubs' | 'requests') || 'clubs';
  const navigate = useNavigate();
  const { userData } = useAuthContext();

  const handleTabChange = useCallback(
    (newTab: 'clubs' | 'requests') => {
      if (newTab === activeTab) return;
      setSearchParams({ tab: newTab });
    },
    [activeTab, setSearchParams]
  );

  return (
    <div className='w-full flex py-4 space-y-4 flex-col flex-1 px-4 sm:px-8'>
      <div className='flex justify-between'>
        <ClubHeader />
        {userData?.role === UserRole.Admin && (
          <div className='flex justify-end items-center'>
            <Button
              onClick={() => navigate(`${AppRoutePaths.CLUBS}/create`)}
              className='leading-[20px] text-[14px] flex items-center gap-[8px] py-[8px] px-[14px] rounded-[8px]'
            >
              <Plus className='w-[20px] h-[20px] text-white' />
              Add Club
            </Button>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className='flex space-x-1 sm:mx-8 border-b'>
        <button
          onClick={() => handleTabChange('clubs')}
          className={`px-[12px] py-[8px] text-[16px] font-semibold leading-[24px] rounded-t-lg transition-colors ${
            activeTab === 'clubs'
              ? 'bg-white border-b-2 border-primary text-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Clubs
        </button>
        <button
          onClick={() => handleTabChange('requests')}
          className={`px-[12px] py-[8px] text-[16px] font-semibold leading-[24px] rounded-t-lg transition-colors ${
            activeTab === 'requests'
              ? 'bg-white border-b-2 border-primary text-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Requests
        </button>
      </div>

      {/* Content */}
      {activeTab === 'clubs' ? <ClubTable /> : <RequestsTab />}
    </div>
  );
};

export default Clubs;
