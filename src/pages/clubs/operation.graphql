query ClubTemplates($paginationArgs: PaginationArgs, $filter: ClubTemplatesFilterInput, $orderBy: ClubOrderInput) {
  clubTemplates(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      id
      name
      description
      about
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
      category
      newPost
      hasJoined
      memberCount
      updatedAt
    }
    total
    page
    limit
  }
}

query ClubTemplateById($clubTemplateByIdId: ID!) {
  clubTemplateById(id: $clubTemplateByIdId) {
    id
    name
    description
    about
    category
    newPost
    img {
      id
      filename
      url
    }
  }
}

query AdminClubById($adminClubByIdId: ID!) {
  adminClubById(id: $adminClubByIdId) {
    id
    activatedAt
    memberCount
    clubTemplate {
      id
      name
      description
      about
      category
      newPost
      img {
        id
        filename
        url
      }
    }
  }
}

query AdminClubs($associationId: ID!, $paginationArgs: PaginationArgs, $filter: ClubFilterInput, $orderBy: ClubOrderInput) {
  adminClubs(associationId: $associationId, paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      id
      activatedAt
      lastActivity
      memberCount
      clubTemplate {
        id
        name
        description
        about
        category
        newPost
        img {
          id
          filename
          url
        }
      }
    }
    total
    page
    limit
  }
}

mutation CreateClubTemplate($input: CreateClubTemplateInput!) {
  createClubTemplate(input: $input) {
    id
    name
    description
    about
    category
    img {
      id
      filename
      key
      mimeType
      size
      status
      createdAt
      updatedAt
      url
    }
    newPost
    hasJoined
    memberCount
  }
}

mutation UpdateClubTemplate($input: UpdateClubTemplateInput!) {
  updateClubTemplate(input: $input) {
    id
    name
    description
    about
    category
    img {
      id
      filename
      url
    }
    newPost
  }
}

mutation DeleteClubTemplate($deleteClubTemplateId: ID!) {
  deleteClubTemplate(id: $deleteClubTemplateId)
}

mutation CreateUploadUrl($input: CreateUploadFileInput!) {
  createUploadUrl(input: $input) {
    uploadFile {
      id
      filename
      key
      mimeType
      size
      status
      createdAt
      updatedAt
      url
    }
    presignedUrl {
      url
      fields {
        bucket
        key
        Policy
        X_Amz_Algorithm
        X_Amz_Credential
        X_Amz_Date
        X_Amz_Signature
      }
    }
  }
}

# ClubRequests query with search support for Requests tab
query ClubRequests($paginationArgs: PaginationArgs, $filter: ClubRequestFilterInput, $orderBy: ClubRequestOrderInput) {
  clubRequests(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      category
      clubAbout
      clubDescription
      clubName
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
      }
      createdAt
      id
      status
      user {
        id
        firstName
        lastName
        email
        phone
        dob
        birthdayMonth
        birthdayDay
        dateJoined
        role
        isDeleted
        canUseClubs
      }
    }
    total
    page
    limit
  }
}

mutation UpdateClubRequest($input: UpdateClubRequestInput!) {
  updateClubRequest(input: $input) {
    id
    clubProfile {
      id
      displayName
    }
    clubName
    clubDescription
    category
    clubAbout
    status
    createdAt
  }
}

query UsersRequestedClubCreation($paginationArgs: PaginationArgs, $filter: UsersRequestedClubCreationFilterInput) {
  usersRequestedClubCreation(paginationArgs: $paginationArgs, filter: $filter) {
    items {
      id
      firstName
      lastName
      email
      phone
      dob
      birthdayMonth
      birthdayDay
      dateJoined
      role
      isDeleted
      canUseClubs
    }
    total
    page
    limit
  }
}


mutation ApprovedClubRequestCreation($clubRequestId: ID!, $clubTemplateId: ID!) {
  approvedClubRequestCreation(clubRequestId: $clubRequestId, clubTemplateId: $clubTemplateId) {
    id
    clubName
    clubDescription
    category
    clubAbout
    status
    createdAt
  }
}

mutation RejectedClubRequestCreation($clubRequestId: ID!) {
  rejectedClubRequestCreation(clubRequestId: $clubRequestId) {
    id
    clubName
    clubDescription
    category
    clubAbout
    status
    createdAt
  }
}

