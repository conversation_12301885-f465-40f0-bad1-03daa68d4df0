import { useCallback } from 'react';
import { useCreateUploadUrlMutation } from '../../../generated/graphql';
import { useToast } from '../../../hooks/useToast';
import { S3_CONFIG } from '../../../config/s3';

export const useImageUpload = () => {
  const [createUploadUrl, { loading: isUploading }] = useCreateUploadUrlMutation();
  const { toast } = useToast();

  const uploadImage = useCallback(
    async (file: File): Promise<{ id: string; url: string } | null> => {
      try {
        // Step 1: Get presigned URL from BE
        const { data } = await createUploadUrl({
          variables: {
            input: {
              filename: file.name,
              mimeType: file.type,
              size: file.size,
            },
          },
        });

        if (!data?.createUploadUrl) {
          throw new Error('Failed to get upload URL');
        }

        const { uploadFile, presignedUrl } = data.createUploadUrl;

        // Step 2: Upload file to S3 using presigned URL
        const formData = new FormData();

        // Follow form demo order: key -> acl -> Content-Type -> X-Amz-* -> Policy -> X-Amz-Signature -> file
        formData.append('key', presignedUrl.fields.key || '');
        formData.append('acl', 'private');
        formData.append('Content-Type', file.type);
        formData.append('X-Amz-Credential', presignedUrl.fields.X_Amz_Credential || '');
        formData.append('X-Amz-Algorithm', presignedUrl.fields.X_Amz_Algorithm || '');
        formData.append('X-Amz-Date', presignedUrl.fields.X_Amz_Date || '');
        formData.append('Policy', presignedUrl.fields.Policy || '');
        formData.append('X-Amz-Signature', presignedUrl.fields.X_Amz_Signature || '');
        formData.append('file', file); // File should be last

        // Upload to S3
        const s3UploadUrl = S3_CONFIG.UPLOAD_URL || presignedUrl.url || '';
        const uploadResponse = await fetch(s3UploadUrl, {
          method: 'POST',
          body: formData,
          // Don't set Content-Type header, let browser auto-set for FormData
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload file to S3');
        }

        return {
          id: uploadFile.id,
          url: uploadFile.url || '',
        };
      } catch (error) {
        console.error('Upload failed:', error);
        toast({
          variant: 'destructive',
          title: 'Failed to upload image',
        });
        return null;
      }
    },
    [createUploadUrl, toast]
  );

  return {
    uploadImage,
    isUploading,
  };
};
