import { Button } from '@/components/ui/Button';

interface ClubsTabsProps {
  activeTab: 'clubs' | 'requests';
  setActiveTab: (tab: 'clubs' | 'requests') => void;
}

const ClubsTabs = ({ activeTab, setActiveTab }: ClubsTabsProps) => {
  const tabs = [
    { id: 'clubs', label: 'Clubs' },
    { id: 'requests', label: 'Requests' },
  ] as const;

  return (
    <div className='w-full bg-white rounded-lg'>
      <div className='border-b border-gray-200'>
        <nav className='flex space-x-8 px-6' aria-label='Tabs'>
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              variant='ghost'
              className={`
                py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap rounded-none
                ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              {tab.label}
            </Button>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default ClubsTabs;
