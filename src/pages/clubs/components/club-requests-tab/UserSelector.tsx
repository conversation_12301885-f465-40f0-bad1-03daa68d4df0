import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Check, ChevronDown, Loader2 } from 'lucide-react';
import { useUsersRequestedClubCreationQuery } from '@/generated/graphql';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/Command';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';
import { useDebounce } from '@/hooks/useDebounce';
import { useApolloClient } from '@/providers/ApolloClient';
import { cn, getUserName } from '@/lib/utils';

interface UserSelectorProps {
  selectedUserId: string | null;
  setSelectedUserId: (value: string | null) => void;
  setSelectedUserName: (value: string | null) => void;
  tempUserSearch: string;
  setTempUserSearch: (value: string) => void;
}

const ITEMS_PER_PAGE = 10;

const UserSelector = ({
  selectedUserId,
  setSelectedUserId,
  setSelectedUserName,
  tempUserSearch,
  setTempUserSearch,
}: UserSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const search = useDebounce(tempUserSearch, 500);
  const [searchPlaceholder, setSearchPlaceholder] = useState('Search User');

  const { client } = useApolloClient() || {};

  const {
    data: usersData,
    fetchMore,
    loading,
  } = useUsersRequestedClubCreationQuery({
    variables: {
      paginationArgs: {
        page,
        limit: ITEMS_PER_PAGE,
      },
      filter: {
        search: search.trim() || undefined,
      },
    },
  });

  const users = useMemo(
    () => usersData?.usersRequestedClubCreation?.items ?? [],
    [usersData?.usersRequestedClubCreation?.items]
  );

  const hasLoadMore = users.length >= ITEMS_PER_PAGE;

  const total = useMemo(() => usersData?.usersRequestedClubCreation?.total || 0, [usersData]);
  const hasMore = useMemo(
    () => users.length > 0 && total / ITEMS_PER_PAGE > page,
    [users, total, page]
  );

  const loadMore = async () => {
    if (hasMore && !loading && !isFetchingMore && users.length > 0) {
      setIsFetchingMore(true);
      try {
        await fetchMore({
          variables: {
            paginationArgs: {
              page: page + 1,
              limit: ITEMS_PER_PAGE,
            },
            filter: {
              search: search.trim() || undefined,
            },
          },
        });
        setPage(page + 1);
      } finally {
        setIsFetchingMore(false);
      }
    }
  };

  const loadMoreRef = useIntersectionObserver({
    hasNextPage: hasMore,
    fetchNextPage: loadMore,
    isFetchingNextPage: isFetchingMore,
    threshold: 0.4,
  });

  const filteredUsers = users;

  const displayedUser = users.find((user) => user.id === selectedUserId);

  useEffect(() => {
    setPage(1);
  }, [search, client]);

  return (
    <Popover modal open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={isOpen}
          className={cn(
            'w-full justify-between text-[#191E3B] font-normal p-3 border border-gray-200 rounded-md text-sm bg-white',
            {
              'text-muted-foreground': !displayedUser,
            }
          )}
          disabled={loading && users.length === 0}
        >
          {loading && users.length === 0 ? (
            <Loader2 className='h-4 w-4 animate-spin' />
          ) : displayedUser ? (
            getUserName(displayedUser.firstName, displayedUser.lastName)
          ) : (
            'User'
          )}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-[--radix-popover-trigger-width] p-0'
        onOpenAutoFocus={(event) => event.preventDefault()}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={searchPlaceholder}
            onBlur={() => setSearchPlaceholder('Search user ...')}
            onFocus={() => setSearchPlaceholder('')}
            value={tempUserSearch}
            onValueChange={setTempUserSearch}
            className='h-9'
          />
          <CommandEmpty>
            {loading ? 'Loading...' : users.length === 0 ? 'No Results' : ''}
          </CommandEmpty>
          <CommandGroup>
            <ScrollArea className={cn('', hasLoadMore ? 'h-64' : 'h-fit')}>
              {filteredUsers.map((user) => (
                <CommandItem
                  key={user.id}
                  value={user.email}
                  onSelect={() => {
                    setSelectedUserId(user.id);
                    setSelectedUserName(getUserName(user.firstName, user.lastName));
                    setIsOpen(false);
                  }}
                  className='flex items-center justify-between'
                >
                  <span>{getUserName(user.firstName, user.lastName)}</span>
                  {user.id === selectedUserId && <Check className='h-4 w-4 text-primary' />}
                </CommandItem>
              ))}
              {hasLoadMore && (
                <div ref={loadMoreRef} className='w-full h-8 flex items-center justify-center'>
                  {isFetchingMore && <Loader2 className='h-4 w-4 animate-spin text-primary' />}
                </div>
              )}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default UserSelector;
