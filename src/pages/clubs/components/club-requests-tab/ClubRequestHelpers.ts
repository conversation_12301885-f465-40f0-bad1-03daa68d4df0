import { ClubRequestStatus, ClubCategoryEnum } from '@/generated/graphql';

export function useClubRequestHelpers() {
  const getStatusEnum = (statusLabel: string): ClubRequestStatus | undefined => {
    switch (statusLabel) {
      case 'PENDING':
        return ClubRequestStatus.Pending;
      case 'Approved':
        return ClubRequestStatus.Approved;
      case 'Rejected':
        return ClubRequestStatus.Rejected;
      default:
        return undefined;
    }
  };

  const getCategoryLabel = (category: ClubCategoryEnum | null | undefined) => {
    switch (category) {
      case ClubCategoryEnum.Creative:
        return 'Creative';
      case ClubCategoryEnum.FitnessOutdoor:
        return 'Fitness & Outdoor';
      case ClubCategoryEnum.FoodDrink:
        return 'Food & Drink';
      case ClubCategoryEnum.Hobbies:
        return 'Hobbies';
      case ClubCategoryEnum.SocialFamily:
        return 'Social & Family';
      default:
        return 'N/A';
    }
  };

  return {
    getStatusEnum,
    getCategoryLabel,
  };
}
