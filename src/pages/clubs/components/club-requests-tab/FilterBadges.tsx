import { X } from 'lucide-react';
import { Badge } from '@/components/ui/Badge';
import { ClubCategoryEnum, ClubRequestStatus } from '@/generated/graphql';
import { getClubCategoryLabel } from '@/pages/club-detail/utils';

interface FilterBadgesProps {
  selectedCategory: ClubCategoryEnum | null;
  selectedStatus: ClubRequestStatus | null;
  selectedUserId: string | null;
  selectedUserName?: string | null;
  onClearCategory: () => void;
  onClearStatus: () => void;
  onClearUserId: () => void;
}

export function FilterBadges({
  selectedCategory,
  selectedStatus,
  selectedUserId,
  selectedUserName,
  onClearCategory,
  onClearStatus,
  onClearUserId,
}: FilterBadgesProps) {
  const getStatusLabel = (statusValue: ClubRequestStatus) => {
    switch (statusValue) {
      case ClubRequestStatus.Pending:
        return 'Pending';
      case ClubRequestStatus.Approved:
        return 'Approved';
      case ClubRequestStatus.Rejected:
        return 'Declined';
    }
  };

  return (
    <div className='flex flex-col sm:flex-row gap-2'>
      {selectedCategory && (
        <Badge className='text-sm gap-2 hover:bg-secondary text-primary w-fit bg-secondary font-medium'>
          {getClubCategoryLabel(selectedCategory)}
          <X className='w-4 h-4 cursor-pointer' onClick={onClearCategory} />
        </Badge>
      )}
      {selectedStatus !== null && (
        <Badge className='text-sm gap-2 hover:bg-secondary text-primary w-fit bg-secondary font-medium'>
          {getStatusLabel(selectedStatus)}
          <X className='w-4 h-4 cursor-pointer' onClick={onClearStatus} />
        </Badge>
      )}
      {selectedUserId !== null && (
        <Badge className='text-sm gap-2 hover:bg-secondary text-primary w-fit bg-secondary font-medium'>
          {selectedUserName}
          <X className='w-4 h-4 cursor-pointer' onClick={onClearUserId} />
        </Badge>
      )}
    </div>
  );
}
