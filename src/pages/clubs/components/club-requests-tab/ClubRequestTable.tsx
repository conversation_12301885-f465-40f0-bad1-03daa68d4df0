import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import {
  useClubRequestsQuery,
  ClubRequestOrderByField,
  OrderDirection,
  ClubRequest,
  UserRole,
} from '@/generated/graphql';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import useSort from '@/hooks/useSort';
import SearchInput from '@/components/ui/search-input/SearchInput';
import { TablePagination } from '@/components/ui/table/TablePagination';
import TableData from '@/components/ui/table/TableData';
import { Row, RowSelectionState } from '@tanstack/react-table';

import { generateClubRequestColumns } from './ClubRequestColumns';
import { useClubRequestFilters } from './hooks/useClubRequestFilters';
import { useClubRequestActions } from './hooks/useClubRequestActions';
import { FilterPopover } from './FilterPopover';
import { FilterBadges } from './FilterBadges';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';
import usePagination from '@/hooks/usePagination';
import { useAuthContext } from '@/pages/auth/AuthContext';

const ClubRequestTable = () => {
  const { searchTemp, setSearchTemp } = useSearchQuery('club-requests');
  const { sorting, setSorting } = useSort();
  const { pagination, setPagination } = usePagination();
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const actionCellRef = useRef<HTMLDivElement>(null);

  const { userData, isLoading: isLoadingAuth } = useAuthContext();

  const filterHook = useClubRequestFilters();
  const actionHook = useClubRequestActions();

  // Reset pagination when filters change (search reset is handled by useSearchQuery)
  useEffect(() => {
    setPagination({ pageIndex: 1, pageSize: 10 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    searchTemp,
    filterHook.selectedCategory,
    filterHook.selectedStatus,
    filterHook.selectedUserId,
  ]);

  const { data: requestsData, loading: isLoadingRequests } = useClubRequestsQuery({
    variables: {
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        category: filterHook.selectedCategory ? filterHook.selectedCategory : undefined,
        status: filterHook.selectedStatus !== null ? filterHook.selectedStatus : undefined,
        userId: filterHook.selectedUserId || undefined,
        ...(searchTemp.trim() && { search: searchTemp.trim() }),
      } as any,
      orderBy:
        sorting.length > 0
          ? {
              field: (() => {
                switch (sorting[0]?.id) {
                  case 'clubName':
                    return ClubRequestOrderByField.CreatedAt;
                  case 'createdAt':
                    return ClubRequestOrderByField.CreatedAt;
                  default:
                    return ClubRequestOrderByField.CreatedAt;
                }
              })(),
              direction: sorting[0]?.desc ? OrderDirection.Desc : OrderDirection.Asc,
            }
          : undefined,
    },
    defaultOptions: {
      fetchPolicy: 'cache-and-network',
    },
  });

  const totalRequests = useMemo(() => {
    return requestsData?.clubRequests.total || 0;
  }, [requestsData?.clubRequests.total]);

  const handleRowClick = useCallback(
    (row: Row<ClubRequest>) => {
      const newSelectedId = row.id === selectedRowId ? null : row.id;
      setSelectedRowId(newSelectedId);

      if (newSelectedId && actionCellRef.current) {
        actionCellRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'start',
        });
      }
    },
    [selectedRowId]
  );

  const columns = useMemo(
    () =>
      generateClubRequestColumns({
        isLoading: isLoadingRequests,
        selectedRows,
        selectedRowId,
        actionCellRef,
        showActions: userData?.role === UserRole.Admin,

        setSelectedRows: setSelectedRows,
        onApproveRequest: actionHook.handleApproveRequest,
        onDeclineRequest: actionHook.handleDeclineRequest,
        setSelectedRowId: setSelectedRowId,
      }),
    [
      isLoadingRequests,
      selectedRows,
      selectedRowId,
      userData?.role,
      actionHook.handleApproveRequest,
      actionHook.handleDeclineRequest,
      // Note: actionCellRef is intentionally excluded from dependencies
      // as it's a stable ref that doesn't change between renders
    ]
  );

  const clubsData = useMemo(() => {
    if (isLoadingRequests || isLoadingAuth) {
      return Array(10).fill({});
    }
    return requestsData?.clubRequests.items ?? [];
  }, [isLoadingAuth, isLoadingRequests, requestsData?.clubRequests.items]);

  return (
    <div className='w-full flex flex-col sm:p-8 sm:pb-12 flex-1 rounded-lg'>
      {/* Search and Filters */}
      <div className='pb-4'>
        <div className='w-full gap-2 flex flex-col sm:flex-row items-center justify-between pb-4'>
          <div className='flex w-full items-center gap-2'>
            <SearchInput name='search' onChange={setSearchTemp} value={searchTemp} />
          </div>

          <div className='flex w-full sm:w-auto items-center gap-2'>
            <FilterPopover filters={filterHook} />
          </div>
        </div>

        {/* Active Filter Badges */}
        <FilterBadges
          selectedCategory={filterHook.selectedCategory}
          selectedStatus={filterHook.selectedStatus}
          selectedUserId={filterHook.selectedUserId}
          selectedUserName={filterHook.selectedUserName}
          onClearCategory={() => {
            filterHook.setSelectedCategory(null);
            filterHook.setTempSelectedCategory(null);
          }}
          onClearStatus={() => {
            filterHook.setSelectedStatus(null);
            filterHook.setTempSelectedStatus(null);
          }}
          onClearUserId={() => {
            filterHook.setSelectedUserId(null);
            filterHook.setTempSelectedUserId(null);
            filterHook.setSelectedUserName(null);
            filterHook.setTempSelectedUserName(null);
          }}
        />
      </div>

      {/* Requests Table */}
      <div className='w-full border rounded-lg overflow-auto'>
        <TableHeaderCount title='Requests' total={requestsData?.clubRequests.total || 0} />

        <TableData
          columns={columns}
          data={clubsData}
          pagination={pagination}
          sorting={sorting}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={setSorting}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
          onRowClick={handleRowClick}
        />
        {totalRequests > 0 && (
          <TablePagination
            pageCount={Math.ceil((requestsData?.clubRequests.total || 0) / pagination.pageSize)}
            currentPage={pagination.pageIndex - 1}
            onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
          />
        )}
      </div>
    </div>
  );
};

export default ClubRequestTable;
