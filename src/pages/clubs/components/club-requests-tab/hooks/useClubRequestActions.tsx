import { useCallback } from 'react';
import {
  ClubRequestsDocument,
  ClubRequest,
  useRejectedClubRequestCreationMutation,
} from '@/generated/graphql';
import { toast } from '@/hooks/useToast';
import { ApolloError } from '@apollo/client';
import { useNavigate } from 'react-router-dom';
import { AppRoutePaths } from '@/lib/constants';

export function useClubRequestActions() {
  const [declineClubRequestCreation] = useRejectedClubRequestCreationMutation();
  const navigate = useNavigate();

  const handleApiError = useCallback((error: unknown, errorMessage = 'Operation failed') => {
    if (error instanceof ApolloError) {
      toast({
        variant: 'destructive',
        title: error.graphQLErrors[0]?.message || errorMessage,
      });
    }
  }, []);

  // New workflow: Extract data and navigate to club creation
  const handleApproveRequest = useCallback(
    (clubRequest: ClubRequest) => {
      // Extract the required fields from the club request
      const clubRequestData = {
        category: clubRequest.category,
        clubAbout: clubRequest.clubAbout,
        clubDescription: clubRequest.clubDescription,
        clubName: clubRequest.clubName,
        requestId: clubRequest.id,
      };

      // Navigate to club creation page with pre-filled data
      navigate(`${AppRoutePaths.CLUBS}/create`, {
        state: {
          fromClubRequest: true,
          clubRequestData,
        },
      });
    },
    [navigate]
  );

  const handleDeclineRequest = useCallback(
    async (requestId: string) => {
      try {
        await declineClubRequestCreation({
          variables: {
            clubRequestId: requestId,
          },

          refetchQueries: [ClubRequestsDocument],
        });
      } catch (error) {
        handleApiError(error, 'Failed to decline request');
      }
    },
    [declineClubRequestCreation, handleApiError]
  );

  return {
    // Actions
    handleApproveRequest,
    handleDeclineRequest,
  };
}
