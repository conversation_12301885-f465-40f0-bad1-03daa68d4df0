import { ClubCategoryEnum, ClubRequestStatus } from '@/generated/graphql';
import { useState } from 'react';

export interface ClubRequestFilters {
  // Applied filter states
  selectedCategory: ClubCategoryEnum | null;
  selectedStatus: ClubRequestStatus | null;
  selectedUserId: string | null;
  selectedUserName: string | null;
  isOpenFilters: boolean;

  // Temporary filter states
  tempSelectedCategory: ClubCategoryEnum | null;
  tempSelectedStatus: ClubRequestStatus | null;
  tempSelectedUserId: string | null;
  tempSelectedUserName: string | null;
  tempUserSearch: string;

  // Actions
  setSelectedCategory: (category: ClubCategoryEnum | null) => void;
  setTempSelectedCategory: (category: ClubCategoryEnum | null) => void;
  setSelectedStatus: (status: ClubRequestStatus | null) => void;
  setSelectedUserId: (userId: string | null) => void;
  setSelectedUserName: (userEmail: string | null) => void;
  setTempSelectedStatus: (status: ClubRequestStatus | null) => void;
  setTempSelectedUserId: (userId: string | null) => void;
  setTempSelectedUserName: (userEmail: string | null) => void;
  setTempUserSearch: (search: string) => void;
  handleApplyFilters: () => void;
  handleCancelFilters: () => void;
  onClearAll: () => void;
  handleFilterOpenChange: (open: boolean) => void;
}

export function useClubRequestFilters(): ClubRequestFilters {
  // Applied filter states
  const [selectedCategory, setSelectedCategory] = useState<ClubCategoryEnum | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<ClubRequestStatus | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedUserName, setSelectedUserName] = useState<string | null>(null);

  // Filter popover state
  const [isOpenFilters, setIsOpenFilters] = useState(false);

  // Temporary filter states (used while the filter modal is open)
  const [tempSelectedCategory, setTempSelectedCategory] = useState(selectedCategory);
  const [tempSelectedStatus, setTempSelectedStatus] = useState(selectedStatus);
  const [tempSelectedUserId, setTempSelectedUserId] = useState(selectedUserId);
  const [tempSelectedUserName, setTempSelectedUserName] = useState(selectedUserName);
  const [tempUserSearch, setTempUserSearch] = useState('');

  // Filter management functions
  const handleApplyFilters = () => {
    // Apply temporary filters to actual filter state
    setSelectedCategory(tempSelectedCategory);
    setSelectedStatus(tempSelectedStatus);
    setSelectedUserId(tempSelectedUserId);
    setSelectedUserName(tempSelectedUserName);

    setIsOpenFilters(false);
  };

  const handleCancelFilters = () => {
    // Close modal and reset temporary states to current applied filters
    setIsOpenFilters(false);
    setTempSelectedCategory(selectedCategory);
    setTempSelectedStatus(selectedStatus);
    setTempSelectedUserId(selectedUserId);
    setTempSelectedUserName(selectedUserName);
    setTempUserSearch('');
  };

  const onClearAll = () => {
    // Reset all filters to default values
    const defaultCategory = null;
    const defaultUserId = null;
    const defaultUserEmail = null;

    setSelectedCategory(defaultCategory);
    setSelectedStatus(null);
    setSelectedUserId(defaultUserId);
    setSelectedUserName(defaultUserEmail);
    setTempSelectedCategory(defaultCategory);
    setTempSelectedStatus(null);
    setTempSelectedUserId(defaultUserId);
    setTempSelectedUserName(defaultUserEmail);
    setTempUserSearch('');

    setIsOpenFilters(false);
  };

  const handleFilterOpenChange = (open: boolean) => {
    setIsOpenFilters(open);

    // When closing without applying, reset temporary states
    if (!open) {
      setTempSelectedCategory(selectedCategory);
      setTempSelectedStatus(selectedStatus);
      setTempSelectedUserId(selectedUserId);
      setTempSelectedUserName(selectedUserName);
      setTempUserSearch('');
    }
  };

  return {
    // State
    selectedCategory,
    selectedStatus,
    selectedUserId,
    selectedUserName,
    isOpenFilters,
    tempSelectedCategory,
    tempSelectedStatus,
    tempSelectedUserId,
    tempSelectedUserName,
    tempUserSearch,

    // Actions
    setSelectedCategory,
    setSelectedStatus,
    setSelectedUserId,
    setSelectedUserName,
    setTempSelectedCategory,
    setTempSelectedStatus,
    setTempSelectedUserId,
    setTempSelectedUserName,
    setTempUserSearch,
    handleApplyFilters,
    handleCancelFilters,
    onClearAll,
    handleFilterOpenChange,
  };
}
