import { ListFilter } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { StatusSelector } from './StatusSelector';
import UserSelector from './UserSelector';
import { ClubRequestFilters } from './hooks/useClubRequestFilters';
import { CategorySelector } from '@/components/category-selector/CategorySelector';

interface FilterPopoverProps {
  filters: ClubRequestFilters;
}

export function FilterPopover({ filters }: FilterPopoverProps) {
  const {
    isOpenFilters,
    handleFilterOpenChange,
    selectedCategory,
    selectedStatus,
    selectedUserId,
    tempSelectedCategory,
    setTempSelectedCategory,
    tempSelectedStatus,
    setTempSelectedStatus,
    tempSelectedUserId,
    setTempSelectedUserId,
    setTempSelectedUserName,
    tempUserSearch,
    setTempUserSearch,
    handleApplyFilters,
    handleCancelFilters,
    onClearAll,
  } = filters;
  // Check if any filters are currently applied (not default values)
  const hasActiveFilters = selectedCategory || selectedStatus || selectedUserId;

  return (
    <Popover modal={false} open={isOpenFilters} onOpenChange={handleFilterOpenChange}>
      <PopoverTrigger asChild>
        <Button variant='outline' className='gap-2 w-full text-gray-700 font-semibold sm:w-auto'>
          <ListFilter className='w-5 h-5' />
          Filters
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align='end'
        className='sm:w-80 w-[--radix-popover-trigger-width]'
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <div className='space-y-4'>
          <div className='flex items-center justify-between mb-2'>
            <label className='text-sm font-medium'>Filter by</label>
            {hasActiveFilters && (
              <span className='cursor-pointer text-sm font-medium' onClick={onClearAll}>
                Clear
              </span>
            )}
          </div>

          {/* Club Category Filter */}
          <div>
            <CategorySelector
              selectedCategory={tempSelectedCategory}
              setSelectedCategory={setTempSelectedCategory}
            />
          </div>

          {/* User Filter */}
          <div>
            <UserSelector
              selectedUserId={tempSelectedUserId}
              setSelectedUserId={setTempSelectedUserId}
              setSelectedUserName={setTempSelectedUserName}
              tempUserSearch={tempUserSearch}
              setTempUserSearch={setTempUserSearch}
            />
          </div>

          {/* Request Status Filter */}
          <div>
            <StatusSelector
              selectedStatus={tempSelectedStatus}
              setSelectedStatus={setTempSelectedStatus}
            />
          </div>

          <div className='flex w-full items-center gap-2'>
            <Button onClick={handleCancelFilters} variant='outline' className='w-full'>
              Cancel
            </Button>
            <Button onClick={handleApplyFilters} variant='default' className='w-full'>
              Confirm
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
