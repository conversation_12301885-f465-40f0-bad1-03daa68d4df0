import { useAdminAssociationByIdQuery } from '@/generated/graphql';

interface AssociationDetailHeaderProps {
  associationId: string;
}

const AssociationDetailHeader = ({ associationId }: AssociationDetailHeaderProps) => {
  const { data: associationData, loading: isLoadingAssociation } = useAdminAssociationByIdQuery({
    variables: { adminAssociationByIdId: associationId },
  });

  const association = associationData?.adminAssociationById;

  if (isLoadingAssociation) {
    return (
      <div className='w-full bg-white rounded-lg p-6'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-64 mb-4'></div>
          <div className='h-4 bg-gray-200 rounded w-48'></div>
        </div>
      </div>
    );
  }

  if (!association) {
    return (
      <div className='w-full bg-white rounded-lg p-6'>
        <div className='text-center text-gray-500'>
          <h1 className='text-2xl font-bold mb-2'>Association Not Found</h1>
          <p>The requested association could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className='w-full bg-white rounded-lg p-6'>
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900 mb-2'>{association.name}</h1>
          <div className='flex items-center gap-4 text-sm text-gray-600'>
            <span>Member Count: {association.memberCount || 0}</span>
            <span>Club Member Count: {association.clubMemberCount || 0}</span>
          </div>
        </div>
        <div className='mt-4 sm:mt-0'>
          <div className='text-sm text-gray-500'>
            Created: {new Date(association.createdAt).toLocaleDateString()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssociationDetailHeader;
