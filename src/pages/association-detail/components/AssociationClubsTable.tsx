import { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import { generateAssociationClubColumns } from './AssociationClubColumns';
import { ColumnFiltersState, RowSelectionState, Row } from '@tanstack/react-table';
import usePagination from '@/hooks/usePagination';
import useSort from '@/hooks/useSort';
import { OrderDirection, ClubOrderByEnum, useAdminClubsQuery, UserRole } from '@/generated/graphql';
import { TablePagination } from '@/components/ui/table/TablePagination';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import TableData from '@/components/ui/table/TableData';
import { useNavigate } from 'react-router-dom';
import { AppRoutePaths, DATE_FORMAT_API } from '@/lib/constants';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';
import { useClubFilters } from '../hooks/useClubFilters';
import ClubTableFilter from './ClubTableFilter';
import { format } from 'date-fns';

interface AssociationClubsTableProps {
  associationId: string;
}

const AssociationClubsTable = ({ associationId }: AssociationClubsTableProps) => {
  const { isLoading: isLoadingAuth } = useAuthContext();
  const { pagination, setPagination } = usePagination();
  const { sorting, setSorting } = useSort();
  const { searchTemp, setSearchTemp } = useSearchQuery('association-clubs');
  const clubFilters = useClubFilters();
  const navigate = useNavigate();
  const { userData } = useAuthContext();

  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const actionCellRef = useRef<HTMLDivElement>(null);

  // Reset sorting when component mounts
  useEffect(() => {
    setSorting([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reset pagination when filters change
  useEffect(() => {
    setPagination({ pageIndex: 1, pageSize: 10 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTemp, clubFilters.selectedCategory, clubFilters.dateRange]);

  const { data: clubs, loading: isLoadingClubs } = useAdminClubsQuery({
    variables: {
      associationId,
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        search: searchTemp.trim() || undefined,
        category: clubFilters.selectedCategory || undefined,
        lastActivityDateFrom: clubFilters.dateRange?.from
          ? format(clubFilters.dateRange.from, DATE_FORMAT_API)
          : undefined,
        lastActivityDateTo: clubFilters.dateRange?.to
          ? format(clubFilters.dateRange.to, DATE_FORMAT_API)
          : undefined,
        // Note: Date filtering is not yet supported by the backend API
        // The dateRange filter will be applied client-side until backend support is added
      },
      orderBy:
        sorting.length > 0
          ? {
              field: sorting[0]?.id as ClubOrderByEnum,
              direction: sorting[0]?.desc ? OrderDirection.Desc : OrderDirection.Asc,
            }
          : undefined,
    },
  });

  const clubsData = useMemo(() => {
    if (isLoadingClubs || isLoadingAuth) {
      return Array(10).fill({});
    }
    return clubs?.adminClubs?.items ?? [];
  }, [isLoadingClubs, isLoadingAuth, clubs]);

  const totalClubs = useMemo(() => clubs?.adminClubs?.total ?? 0, [clubs]);

  const handleRowClick = useCallback(
    (row: Row<any>) => {
      const newSelectedId = row.id === selectedRowId ? null : row.id;
      setSelectedRowId(newSelectedId);

      if (newSelectedId && actionCellRef.current) {
        actionCellRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'start',
        });
      }
    },
    [selectedRowId]
  );

  const columns = useMemo(
    () =>
      generateAssociationClubColumns({
        isLoading: isLoadingClubs,
        showActions: true,
        canEdit: userData?.role === UserRole.Admin,
        selectedRowId,
        onNavigate: (clubId: string) =>
          navigate(`${AppRoutePaths.ASSOCIATIONS}/${associationId}/clubs/${clubId}`),
        onEditClub: (clubTemplateId: string) =>
          navigate(`${AppRoutePaths.CLUBS}/${clubTemplateId}`),
        setSelectedRowId,
        actionCellRef,
      }),
    [isLoadingClubs, userData?.role, selectedRowId, navigate, associationId]
  );

  return (
    <div className='w-full flex flex-col sm:p-8 sm:pb-12 flex-1 rounded-lg'>
      {/* Search and Filters */}
      <ClubTableFilter
        search={searchTemp}
        setSearch={setSearchTemp}
        selectedCategory={clubFilters.selectedCategory}
        setSelectedCategory={clubFilters.setSelectedCategory}
        dateRange={clubFilters.dateRange}
        setDateRange={clubFilters.setDateRange}
      />

      <div className='border rounded-xl overflow-hidden'>
        <TableHeaderCount title='Clubs' total={totalClubs} />

        {/* Table */}
        <TableData
          columns={columns}
          data={clubsData}
          pagination={pagination}
          sorting={sorting}
          filters={columnFilters}
          onColumnFiltersChange={setColumnFilters}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={setSorting}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
          onRowClick={handleRowClick}
        />

        {/* Pagination */}
        {totalClubs > 0 && (
          <TablePagination
            pageCount={Math.ceil(totalClubs / pagination.pageSize)}
            currentPage={pagination.pageIndex - 1}
            onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
          />
        )}
      </div>
    </div>
  );
};

export default AssociationClubsTable;
