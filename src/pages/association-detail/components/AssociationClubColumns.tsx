import { Checkbox } from '@/components/ui/Checkbox';
import ActionCell from '@/components/ui/table/ActionCell';
import { HeaderColumn } from '@/components/ui/table/HeaderColumn';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { AdminClub, ClubCategoryEnum, ClubOrderByEnum } from '@/generated/graphql';
import { DATE_FORMAT } from '@/lib/constants';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';

interface AssociationClubColumnsProps {
  isLoading: boolean;
  showActions: boolean;
  selectedRowId: string | null;
  actionCellRef?: React.RefObject<HTMLDivElement>;
  canEdit: boolean;
  onNavigate: (clubId: string) => void;
  onEditClub: (clubTemplateId: string) => void;
  setSelectedRowId: (id: string | null) => void;
}

export function generateAssociationClubColumns({
  isLoading,
  showActions,
  actionCellRef,
  selectedRowId,
  canEdit,
  onNavigate,
  onEditClub,
  setSelectedRowId,
}: AssociationClubColumnsProps) {
  const columns: ColumnDef<AdminClub>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      maxSize: 50,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: `${ClubOrderByEnum.Name}`,
      header: ({ column }) => <HeaderColumn column={column}>Club Name</HeaderColumn>,
      cell: ({ row }) => {
        const club = row.original;
        const clubName = club?.clubTemplate?.name || '';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-gray-900 font-medium'>{clubName}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 250,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'category',
      header: ({ column }) => <HeaderColumn column={column}>Club Category</HeaderColumn>,
      cell: ({ row }) => {
        const category = row.original?.clubTemplate?.category;
        const getCategoryLabel = (category: ClubCategoryEnum | null | undefined) => {
          switch (category) {
            case ClubCategoryEnum.Creative:
              return 'Creative';
            case ClubCategoryEnum.FitnessOutdoor:
              return 'Fitness & Outdoor';
            case ClubCategoryEnum.FoodDrink:
              return 'Food & Drink';
            case ClubCategoryEnum.Hobbies:
              return 'Hobbies';
            case ClubCategoryEnum.SocialFamily:
              return 'Social & Family';
            default:
              return 'N/A';
          }
        };

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>
                {getCategoryLabel(category)}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 150,
      size: 180,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'description',
      header: ({ column }) => <HeaderColumn column={column}>Description</HeaderColumn>,
      cell: ({ row }) => {
        const description = row.original?.clubTemplate?.description;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>{description}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 200,
      size: 300,
    },
    {
      accessorKey: 'memberCount',
      header: ({ column }) => <HeaderColumn column={column}>Total Members</HeaderColumn>,
      cell: ({ row }) => {
        const memberCount = row.original?.memberCount || 0;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>{memberCount}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
      enableSorting: false,
    },
    {
      accessorKey: `${ClubOrderByEnum.LastActivity}`,
      header: ({ column }) => <HeaderColumn column={column}>Last Activity</HeaderColumn>,
      cell: ({ row }) => {
        const lastActivity = row.original.lastActivity;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>
                {lastActivity ? format(lastActivity, DATE_FORMAT) : ''}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
      meta: {
        padding: '20px 16px',
      },
    },
  ];

  if (showActions) {
    columns.push({
      accessorKey: 'actions',
      header: '',
      cell: ({ row }) => {
        const club = row.original;
        const isSelected = selectedRowId === row.id;

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <ActionCell
              ref={actionCellRef}
              isSelected={isSelected}
              setSelectedRowId={setSelectedRowId}
              actions={[
                {
                  label: 'View Club Details',
                  onClick: () => onNavigate(club.id),
                },
                ...(canEdit
                  ? [
                      {
                        label: 'Edit Club Details',
                        onClick: () => club.clubTemplate?.id && onEditClub(club.clubTemplate?.id),
                      },
                    ]
                  : []),
              ]}
            />
          </SkeletonCell>
        );
      },
      minSize: 80,
      maxSize: 80,
      meta: {
        padding: '20px 16px',
      },
    });
  }

  return columns;
}
