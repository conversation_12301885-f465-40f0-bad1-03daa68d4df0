import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import { ClubCategoryEnum } from '@/generated/graphql';

export interface ClubFilters {
  // Applied filter states
  selectedCategory: ClubCategoryEnum | null;
  dateRange: DateRange | undefined;

  // Temporary filter states
  tempSelectedCategory: ClubCategoryEnum | null;
  tempDateRange: DateRange | undefined;
  isOpenFilters: boolean;

  // Actions
  setSelectedCategory: (category: ClubCategoryEnum | null) => void;
  setDateRange: (dateRange: DateRange | undefined) => void;
  setTempSelectedCategory: (category: ClubCategoryEnum | null) => void;
  setTempDateRange: (dateRange: DateRange | undefined) => void;
  handleApplyFilters: () => void;
  handleCancelFilters: () => void;
  onClearAll: () => void;
  handleFilterOpenChange: (open: boolean) => void;
}

export function useClubFilters(): ClubFilters {
  // Applied filter states
  const [selectedCategory, setSelectedCategory] = useState<ClubCategoryEnum | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);

  // Filter popover state
  const [isOpenFilters, setIsOpenFilters] = useState(false);

  // Temporary filter states (used while the filter modal is open)
  const [tempSelectedCategory, setTempSelectedCategory] = useState<ClubCategoryEnum | null>(
    selectedCategory
  );
  const [tempDateRange, setTempDateRange] = useState<DateRange | undefined>(dateRange);

  // Filter management functions
  const handleApplyFilters = () => {
    // Apply temporary filters to actual filter state
    setSelectedCategory(tempSelectedCategory);
    setDateRange(tempDateRange);
    setIsOpenFilters(false);
  };

  const handleCancelFilters = () => {
    // Close modal and reset temporary states to current applied filters
    setIsOpenFilters(false);
    setTempSelectedCategory(selectedCategory);
    setTempDateRange(dateRange);
  };

  const onClearAll = () => {
    // Reset all filters to default values
    const defaultCategory = null;
    const defaultDateRange = undefined;

    setSelectedCategory(defaultCategory);
    setDateRange(defaultDateRange);
    setTempSelectedCategory(defaultCategory);
    setTempDateRange(defaultDateRange);
    setIsOpenFilters(false);
  };

  const handleFilterOpenChange = (open: boolean) => {
    setIsOpenFilters(open);

    // When closing without applying, reset temporary states
    if (!open) {
      setTempSelectedCategory(selectedCategory);
      setTempDateRange(dateRange);
    }
  };

  return {
    // Applied filter states
    selectedCategory,
    dateRange,

    // Temporary filter states
    tempSelectedCategory,
    tempDateRange,
    isOpenFilters,

    // Actions
    setSelectedCategory,
    setDateRange,
    setTempSelectedCategory,
    setTempDateRange,
    handleApplyFilters,
    handleCancelFilters,
    onClearAll,
    handleFilterOpenChange,
  };
}
