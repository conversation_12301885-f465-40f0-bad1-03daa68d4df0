import { useParams } from 'react-router-dom';
import AssociationClubsTable from './components/AssociationClubsTable';
import AssociationHeader from '../associations/components/AssociationHeader';

const AssociationDetail = () => {
  const { id } = useParams<{ id: string }>();

  if (!id) {
    return <div>Association not found</div>;
  }

  return (
    <div className='w-full flex py-4 space-y-4 flex-col flex-1 px-4 sm:px-8'>
      <AssociationHeader />
      <AssociationClubsTable associationId={id} />
    </div>
  );
};

export default AssociationDetail;
