import { Separator } from '@/components/ui/Separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/Tabs';
import { useQueryState } from 'nuqs';
import ProfileInfo from './profile-info/ProfileInfo';
import { PermissionTab } from './permission/Loadable';
import PermissionHeader from './permission/components/PermissionHeader';
import { useMultiFormGuardContext } from '@/hooks/useMultiFormGuard';
import { useRef } from 'react';

// Define available tab types
export enum TabType {
  PROFILE = 'profile',
  PERMISSIONS = 'permissions',
}

/**
 * SettingsPage Component
 * Renders a tabbed interface for managing user settings
 */
const SettingsPage = () => {
  // Use nuqs to persist tab state in URL
  const [activeTab, setActiveTab] = useQueryState('tab', {
    defaultValue: TabType.PROFILE,
    parse: (value) =>
      Object.values(TabType).includes(value as TabType) ? (value as TabType) : TabType.PROFILE,
  });

  const { requestTransition } = useMultiFormGuardContext();
  const pendingTabRef = useRef<TabType | null>(null);

  return (
    <div className='flex flex-col space-y-8 py-8 px-4 sm:px-8'>
      <PermissionHeader currentTab={activeTab} />
      <Tabs
        value={activeTab}
        onValueChange={(value) => {
          if (value !== activeTab) {
            pendingTabRef.current = value as TabType;
            requestTransition(() => {
              setActiveTab(pendingTabRef.current!);
            });
          }
        }}
        className='mt-8 '
      >
        <TabsList className='h-auto p-0 gap-4 w-full rounded-none border-b border-b-[#EBEDF4] justify-start bg-transparent'>
          <TabsTrigger
            value='profile'
            className='rounded-none  text-muted-foreground text-base py-4 flex flex-col items-center bg-none px-0  data-[state=active]:!bg-inherit data-[state=active]:text-primary data-[state=active]:shadow-none relative'
          >
            <div className='font-semibold text-[14px] leading-5'>Profile Info</div>
            {/* Active Tab Indicator */}
            {activeTab === TabType.PROFILE && (
              <Separator className='w-full h-0.5 bg-primary absolute bottom-0' />
            )}
          </TabsTrigger>

          {/* Permissions Tab */}
          <TabsTrigger
            value={TabType.PERMISSIONS}
            className='rounded-none text-muted-foreground text-base py-4 flex flex-col items-center bg-none px-0 data-[state=active]:!bg-inherit data-[state=active]:text-primary data-[state=active]:shadow-none relative'
          >
            <div className='font-semibold text-[14px] leading-5'>Permissions</div>
            {/* Active Tab Indicator */}
            {activeTab === TabType.PERMISSIONS && (
              <Separator className='w-full h-0.5 bg-primary absolute bottom-0' />
            )}
          </TabsTrigger>
        </TabsList>

        {/* Tab Content */}
        <TabsContent value={TabType.PROFILE} className='py-4 sm:py-8'>
          <ProfileInfo />
        </TabsContent>
        <TabsContent value={TabType.PERMISSIONS} className='py-4 sm:py-8'>
          <PermissionTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SettingsPage;
