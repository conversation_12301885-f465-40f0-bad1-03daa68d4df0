import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropDownMenu';

import { Button } from '@/components/ui/Button';

import { MoreVertical } from 'lucide-react';
import { forwardRef, useCallback } from 'react';

type UserActionCellProps = {
  onEditUser: () => void;
  onRemoveUser: () => void;
  isRowSelected: boolean;
  setSelectedRowId: (userId: string | null) => void;
};

export const UserActionCell = forwardRef<HTMLDivElement, UserActionCellProps>(
  ({ onEditUser, onRemoveUser, isRowSelected, setSelectedRowId }, ref) => {
    const handleOpenChange = useCallback(
      (open: boolean) => {
        if (!open) {
          setSelectedRowId(null);
        }
      },
      [setSelectedRowId]
    );

    return (
      <div ref={ref}>
        <DropdownMenu open={isRowSelected} onOpenChange={handleOpenChange}>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <MoreVertical className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='w-60'>
            <DropdownMenuItem onClick={onEditUser}>Edit User</DropdownMenuItem>
            <DropdownMenuItem onClick={onRemoveUser}>Remove User</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }
);
