import { Plus } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import CreateDashboardUserModal from '@/pages/settings/profile-info/components/create-dashboard-user-modal/CreateDashboardUserModal';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { UserRole } from '@/generated/graphql';
import { TabType } from '../../SettingsPage';
type ComponentType = {
  currentTab?: TabType;
};

const PermissionHeader: React.FC<ComponentType> = (props: ComponentType) => {
  const { currentTab } = props;
  const [isOpen, setIsOpen] = useState(false);
  const { userData } = useAuthContext();
  const isAdminUser = userData?.role === UserRole.Admin;

  return (
    <div className='w-full flex items-start sm:items-center gap-2 flex-col sm:flex-row justify-between'>
      <h2 className='sm:text-3xl text-2xl h-11 font-medium'>Settings</h2>
      {isAdminUser && currentTab === TabType.PERMISSIONS && (
        <Button className='gap-2' onClick={() => setIsOpen(true)}>
          <Plus className='w-4 h-4' />
          Add Dashboard User
        </Button>
      )}
      <CreateDashboardUserModal
        open={isOpen}
        onOpenChange={setIsOpen}
        onCancel={() => setIsOpen(false)}
      />
    </div>
  );
};

export default PermissionHeader;
