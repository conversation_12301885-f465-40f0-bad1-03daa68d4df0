# Mutation to update user profile information
# @param input UpdateProfileInput object containing profile data
mutation UpdateProfile($input: UpdateProfileInput!) {
  updateProfile(input: $input) {
    id
    firstName
    lastName
    email
  }
}

# Mutation to update user password
# @param input UpdatePasswordInput object containing password data
mutation UpdatePassword($input: UpdatePasswordInput!) {
  updatePassword(input: $input) {
    # Response field containing operation result
    message # Success/failure message from the server
  }
}
