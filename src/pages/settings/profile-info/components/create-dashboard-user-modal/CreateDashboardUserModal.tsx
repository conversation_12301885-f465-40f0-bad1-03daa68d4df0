import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/Dialog';
import FeatureIcon from '@/assets/images/user/featured-icon.svg';
import { ListStaffDocument, useRegisterMutation, UserRole } from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';
import DashboardUserForm, {
  DashboardUserFormSchema,
} from '@/components/dashboard-user-form/DashboardUserForm';
import { ApolloError, useApolloClient } from '@apollo/client';

interface CreateDashboardUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
}

const CreateDashboardUserModal = ({
  open,
  onOpenChange,
  onCancel,
}: CreateDashboardUserModalProps) => {
  const { toast } = useToast();
  const client = useApolloClient();
  const [createDashboardUser, { loading }] = useRegisterMutation();

  const handleCreateDashboardUser = async (data: DashboardUserFormSchema) => {
    try {
      const response = await createDashboardUser({
        variables: {
          input: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            role: data.role as unknown as UserRole,
          },
        },
      });

      client.refetchQueries({
        include: [ListStaffDocument],
      });

      toast({
        variant: 'success',
        title: response.data?.register.message,
      });
      onCancel();
    } catch (error) {
      console.error('Error creating user:', error);

      if (error instanceof ApolloError) {
        const message = error.graphQLErrors[0].message;
        toast({
          variant: 'destructive',
          title: message,
        });
      }
    }
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        hideClose
        className='sm:max-w-[552px] max-w-[90vw] max-h-[90dvh] overflow-y-auto rounded-lg'
        onOpenAutoFocus={(event) => event.preventDefault()}
      >
        <DialogHeader className='flex items-start gap-2'>
          <img src={FeatureIcon} alt='logo' width={48} height={48} />
          <DialogTitle className='text-2xl font-semibold'>New Dashboard User</DialogTitle>
        </DialogHeader>
        <DashboardUserForm
          onCancel={onCancel}
          onConfirm={handleCreateDashboardUser}
          isLoading={loading}
        />
      </DialogContent>
    </Dialog>
  );
};

export default CreateDashboardUserModal;
