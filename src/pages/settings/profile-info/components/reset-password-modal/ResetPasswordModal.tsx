import LockIcon from '@/assets/images/user/lock-icon.svg';
import { Button } from '@/components/ui/Button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/Dialog';
import { Form } from '@/components/ui/form/Form';
import { FormInput } from '@/components/ui/form/FormInput';
import { UpdatePasswordInput } from '@/generated/graphql';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';

type ComponentType = {
  open: boolean;
  onOpenChange?: (open: boolean) => void;
  onCancel?: () => void;
  onConfirm: (formValue: UpdatePasswordInput) => void;
  isLoading?: boolean;
};

const passwordValidation = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Za-z]/, 'Password must contain at least one letter')
  .regex(/\d/, 'Password must contain at least one number')
  .regex(/[^a-zA-Z0-9]/, 'Password must contain at least one special character');

const schema = z
  .object({
    // Validate current password with the defined password rules
    currentPassword: passwordValidation,
    // Validate new password with the same password rules
    newPassword: passwordValidation,
    // Validate confirmation of the new password with the same rules
    confirmNewPassword: passwordValidation,
  })
  .superRefine((data, ctx) => {
    if (data.newPassword !== data.confirmNewPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['confirmNewPassword'],
        message: 'Passwords do not match',
      });
    }
    if (data.currentPassword === data.newPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['newPassword'],
        message: 'New password must be different from current password',
      });
    }
  });

const ResetPasswordModal = ({
  open,
  onCancel,
  onConfirm,
  isLoading,
  onOpenChange,
}: ComponentType) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        hideClose
        className='sm:max-w-[552px] max-w-[90vw] rounded-lg'
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader className='text-center'>
          <div className='flex justify-center'>
            <img src={LockIcon} alt='' width={48} height={48} />
          </div>
          <DialogTitle className='text-lg font-semibold leading-7 space-y-[18px] text-center'>
            Password Reset
          </DialogTitle>
          <p className='text-[14px] font-normal leading-[20px] text-center'>
            Please enter your new password
          </p>
        </DialogHeader>
        <Form
          mode='onChange'
          schema={schema}
          onSubmit={(values) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { confirmNewPassword, ...rest } = values;
            onConfirm(rest);
          }}
          className='space-y-6'
        >
          <FormInput
            name='currentPassword'
            label='Current Password'
            placeholder='Current Password'
            type='password'
            showPasswordToggle
          />
          <FormInput
            name='newPassword'
            label='New Password'
            placeholder='New Password'
            type='password'
            showPasswordToggle
          />
          <FormInput
            name='confirmNewPassword'
            label='Confirm New Password'
            placeholder='Confirm New Password'
            type='password'
            showPasswordToggle
          />
          <FormSubmitButtons onCancel={onCancel} isLoading={isLoading} />
        </Form>
      </DialogContent>
    </Dialog>
  );
};

const FormSubmitButtons = ({
  onCancel,
  isLoading,
}: {
  onCancel?: () => void;
  isLoading?: boolean;
}) => {
  const { formState } = useFormContext();

  return (
    <div className='flex flex-col sm:flex-row justify-center w-full gap-3'>
      <Button
        variant='outline'
        className='w-full text-base font-semibold'
        type='button'
        onClick={onCancel}
        disabled={isLoading}
      >
        Cancel
      </Button>
      <Button
        loading={isLoading}
        className='w-full text-base font-semibold'
        type='submit'
        disabled={!formState.isValid}
      >
        Reset Password
      </Button>
    </div>
  );
};

export default ResetPasswordModal;
