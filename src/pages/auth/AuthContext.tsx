import {
  AuthToken,
  LoginInput,
  User,
  useMeLazyQuery,
  useLoginMutation,
  useRefreshTokenMutation,
} from '@/generated/graphql';
import { refreshTokenVar } from '@/lib/cache';
import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useApolloClient } from '@/providers/ApolloClient';

interface AuthContextValue {
  isAuthenticated: boolean;
  isLoading: boolean;
  authToken?: AuthToken;
  userData?: User;
  userDataLoading: boolean;
  setAuthState: (authenticated: boolean, authToken: AuthToken) => void;
  signIn: (value: LoginInput) => Promise<void>;
  signOut: () => Promise<void>;
  signInLoading: boolean;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [authState, setAuthState] = useState<{
    isAuthenticated: boolean;
    isLoading: boolean;
    authToken?: AuthToken;
  }>({
    isAuthenticated: false,
    isLoading: true,
  });
  const [userData, setUserData] = useState<User | undefined>(undefined);
  const { client } = useApolloClient() || {};

  const [signInMutate, { loading: signInLoading }] = useLoginMutation();
  const [refreshToken] = useRefreshTokenMutation();
  const [getMeQuery, { loading: userDataLoading }] = useMeLazyQuery();

  const fetchUserData = useCallback(async (): Promise<void> => {
    if (!authState.isAuthenticated) return;

    try {
      const { data } = await getMeQuery({
        fetchPolicy: 'network-only',
      });
      if (data?.me) {
        setUserData(data.me);
      }
    } catch (error) {
      console.error('Failed to fetch user data:', error);
    }
  }, [authState.isAuthenticated, getMeQuery]);

  const updateAuthState = useCallback(
    (isAuthenticated: boolean, authToken?: AuthToken): void => {
      setAuthState({ isAuthenticated, isLoading: false, authToken });
      setUserData(undefined);

      if (!isAuthenticated) {
        // Reset Apollo store when logging out
        client?.clearStore();
      }
    },
    [client]
  );

  useEffect(() => {
    const initializeAuth = async () => {
      const authData = localStorage.getItem('auth');
      if (!authData) {
        updateAuthState(false);
        return;
      }
      const token = JSON.parse(authData);
      updateAuthState(true, token);
    };

    initializeAuth().catch((error) => {
      console.error('Failed to initialize authentication:', error);
      updateAuthState(false);
    });
  }, [updateAuthState, fetchUserData]);

  // Fetch user data after authentication only if not already loaded
  useEffect(() => {
    if (authState.isAuthenticated && !userData) {
      fetchUserData();
    }
  }, [authState.isAuthenticated, userData, fetchUserData]);

  const signIn = useCallback(
    async (input: LoginInput): Promise<void> => {
      try {
        const res = await signInMutate({
          variables: {
            input,
          },
        });

        if (res.data?.login) {
          // Reset Apollo store to clear any cached data
          await client?.clearStore();

          localStorage.setItem('auth', JSON.stringify(res.data.login));
          updateAuthState(true, res.data.login);

          // Fetch user data with new token
          await fetchUserData();
        } else {
          console.error('Login response does not contain data');
        }
      } catch (error) {
        console.error('Failed to sign in:', error);
        throw error;
      }
    },
    [signInMutate, updateAuthState, fetchUserData, client]
  );

  const signOut = useCallback(async (): Promise<void> => {
    try {
      await client?.clearStore();
      localStorage.removeItem('auth');
      updateAuthState(false);
    } catch (error) {
      console.error('Failed to sign out:', error);
    }
  }, [updateAuthState, client]);

  const refreshUserData = useCallback(async (): Promise<void> => {
    await fetchUserData();
  }, [fetchUserData]);

  useEffect(() => {
    refreshTokenVar(async () => {
      const authData = localStorage.getItem('auth');
      if (!authData) {
        await signOut();
        return undefined;
      }

      const { refreshToken: currentRefreshToken } = JSON.parse(authData);

      if (!currentRefreshToken) {
        await signOut();
        return undefined;
      }

      try {
        const data = await refreshToken({
          variables: {
            input: {
              refreshToken: currentRefreshToken,
            },
          },
        });

        if (!data.data?.refreshToken) {
          await signOut();
          return undefined;
        }

        localStorage.setItem('auth', JSON.stringify(data.data.refreshToken));
        updateAuthState(true, data.data.refreshToken);
        return data.data.refreshToken;
      } catch (error: any) {
        // If refresh token is expired or invalid, sign out immediately
        if (error?.graphQLErrors?.[0]?.extensions?.code === 'UNAUTHORIZED') {
          await signOut();
          return undefined;
        }
        // For other errors, also sign out to be safe
        await signOut();
        return undefined;
      }
    });
  }, [signOut, refreshToken, updateAuthState]);

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        userData,
        userDataLoading,
        setAuthState: updateAuthState,
        signIn,
        signOut,
        signInLoading,
        refreshUserData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuthContext = (): AuthContextValue => {
  const context = useContext(AuthContext) as AuthContextValue;
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};
