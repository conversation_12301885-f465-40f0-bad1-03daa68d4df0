import * as z from 'zod';
import { Button } from '@/components/ui/Button';
import { Link, useNavigate } from 'react-router-dom';
import { AppRoutePaths } from '@/lib/constants';
import authBackground from '@/assets/images/auth/auth-img.jpg';
import { FormInput } from '@/components/ui/form/FormInput';
import { Form, FormLabel } from '@/components/ui/form/Form';
import { UseFormReturn } from 'react-hook-form';
import { ApolloError } from '@apollo/client';
import { useToast } from '@/hooks/useToast';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { emailSchema } from '@/lib/schemas';
import FooterCopyright from '@/components/footer/FooterCopyright';
import UnityLogo from '@/components/unity-logo/UnityLogo';
import { useHideLogoOnZoom } from '@/hooks/useZoomLevel';

const signInFormSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

type SignInFormValues = z.infer<typeof signInFormSchema>;

const defaultValues: SignInFormValues = {
  email: '',
  password: '',
};

export const SignIn = () => {
  const { signIn, signInLoading } = useAuthContext();
  const navigate = useNavigate();
  const { toast } = useToast();
  const isHideLogo = useHideLogoOnZoom();

  const onSubmit = async (
    values: SignInFormValues,
    formHandler: UseFormReturn<SignInFormValues>
  ) => {
    try {
      values.email = values.email.toLowerCase();
      await signIn(values);
      navigate(AppRoutePaths.USERS);
    } catch (error: any) {
      console.error('error', error);

      if (error instanceof ApolloError) {
        const errorMessage = error.graphQLErrors[0]?.message;
        const errorCode = error.graphQLErrors[0]?.extensions?.code;

        if (errorMessage === 'Incorrect email or password' || errorCode === 'INVALID_CREDENTIALS') {
          return formHandler.setError('password', { message: errorMessage });
        } else {
          return toast({
            title: errorMessage,
            variant: 'destructive',
          });
        }
      }
    }
  };

  return (
    <div className='flex relative'>
      {/* Logo */}
      {!isHideLogo && (
        <div className='absolute top-10 left-4 sm:left-10'>
          <UnityLogo />
        </div>
      )}
      {/* Left side - Form */}
      <div className='w-full sm:w-1/2 mt-20 sm:mt-0 flex flex-col justify-start sm:justify-center items-center p-4 sm:p-8 overflow-y-auto max-h-screen'>
        <div className='w-full sm:max-w-[360px]'>
          {/* Login Form */}
          <div className='gap-3 flex flex-col'>
            <h1 className='text-2xl sm:text-4xl text-gray-900 font-semibold mb-1'>Log in</h1>
            <p className='text-sm '>Welcome back! Please enter your details.</p>
          </div>

          <Form
            mode='onSubmit'
            onSubmit={(values, formHandler) => onSubmit(values, formHandler!)}
            schema={signInFormSchema}
            defaultValues={defaultValues}
            className='w-full'
          >
            <div className=' space-y-4 mt-8'>
              <div>
                <FormLabel className='text-sm font-medium text-gray-700 block mb-1'>
                  Email
                </FormLabel>
                <FormInput name='email' placeholder='Enter you email' className='w-full ' />
              </div>

              <div>
                <div className='flex justify-between items-center mb-1'>
                  <FormLabel className='text-sm font-medium text-gray-700'>Password</FormLabel>
                </div>
                <FormInput
                  name='password'
                  type='password'
                  placeholder='••••••••'
                  className='w-full'
                  showPasswordToggle
                />
              </div>

              <Button
                asChild
                variant={'link'}
                className='text-sm p-0 h-auto text-[#586AB6] font-semibold'
              >
                <Link to={AppRoutePaths.FORGOT_PASSWORD}>Forgot password</Link>
              </Button>

              <Button
                className='w-full mt-2 font-semibold'
                type='submit'
                disabled={signInLoading}
                loading={signInLoading}
              >
                Sign in
              </Button>
            </div>
          </Form>
        </div>
      </div>

      {/* Right side - Image */}
      <div className='hidden sm:block sm:w-1/2 h-screen'>
        <img src={authBackground} alt='Unity community' className='w-full h-full object-cover' />
      </div>
      {!isHideLogo && <FooterCopyright />}
    </div>
  );
};
