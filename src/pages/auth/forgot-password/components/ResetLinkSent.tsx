import mailIcon from '@/assets/images/mail-icon.svg';
import { Button } from '@/components/ui/Button';
import { AppRoutePaths } from '@/lib/constants';
import { useNavigate } from 'react-router-dom';

const ResetLinkSent = () => {
  const navigate = useNavigate();
  return (
    <div className='mt-20 sm:mt-0 w-full h-full flex flex-col justify-start sm:justify-center items-center flex-1 sm:max-w-[360px]'>
      <div className='gap-4 sm:gap-8 flex flex-col'>
        <div className='flex justify-start items-start '>
          <img src={mailIcon} alt='mail' className='w-12 h-12' />
        </div>
        <h1 className='text-2xl sm:text-4xl text-gray-900 font-semibold mb-1'>Reset link sent</h1>
        <p className='text-base'>
          If an account with that email exists, we&apos;ve sent a password reset link. Check your
          inbox and follow the instructions.
        </p>
        <Button className='w-full' onClick={() => navigate(AppRoutePaths.SIGN_IN)}>
          Back to log in
        </Button>
      </div>
    </div>
  );
};

export default ResetLinkSent;
