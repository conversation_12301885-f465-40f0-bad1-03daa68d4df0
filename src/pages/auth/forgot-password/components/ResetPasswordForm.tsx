import { Button } from '@/components/ui/Button';
import { Form, FormLabel } from '@/components/ui/form/Form';
import { FormInput } from '@/components/ui/form/FormInput';
import { AppRoutePaths } from '@/lib/constants';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import { z } from 'zod';
import { useForgotPasswordMutation } from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';
import { emailSchema } from '@/lib/schemas';

const resetPasswordFormSchema = z.object({
  email: emailSchema,
});

type ResetPasswordFormValues = z.infer<typeof resetPasswordFormSchema>;

const defaultValues: ResetPasswordFormValues = {
  email: '',
};

interface ResetPasswordFormProps {
  setIsResetLinkSent: (isResetLinkSent: boolean) => void;
}

const ResetPasswordForm = ({ setIsResetLinkSent }: ResetPasswordFormProps) => {
  const { toast } = useToast();
  const [forgotPassword, { loading: forgotPasswordLoading }] = useForgotPasswordMutation();

  const onSubmit = async (values: ResetPasswordFormValues) => {
    try {
      const response = await forgotPassword({
        variables: {
          input: {
            email: values.email,
          },
        },
      });

      if (response.data?.forgotPassword.message) {
        setIsResetLinkSent(true);
      }
    } catch (error) {
      if (error instanceof Error) {
        return toast({
          variant: 'destructive',
          title: error.message,
        });
      }
      console.error('Forgot password error:', error);
    }
  };

  return (
    <div className='w-full sm:max-w-[360px]'>
      {/* Login Form */}
      <div className='gap-3 flex flex-col'>
        <div className='flex items-center gap-2'>
          <Link
            to={AppRoutePaths.SIGN_IN}
            className='hover:text-primary hover:bg-primary/10 rounded-full p-1'
          >
            <ArrowLeft className='w-5 h-5' />
          </Link>
        </div>
        <h1 className='text-2xl sm:text-4xl text-gray-900 font-semibold mb-1'>Reset Password</h1>
        <p className='text-base'>We&apos;ll send you a password reset link.</p>
      </div>

      <Form
        mode='onChange'
        onSubmit={(values) => onSubmit(values)}
        schema={resetPasswordFormSchema}
        defaultValues={defaultValues}
        className='w-full'
      >
        <div className='space-y-4 sm:space-y-8 mt-8'>
          <div>
            <FormLabel className='text-sm font-medium text-gray-700 block mb-1'>Email</FormLabel>
            <FormInput name='email' placeholder='Enter you email' className='w-full ' />
          </div>

          <Button
            disableOnInvalid
            className='w-full mt-2'
            type='submit'
            disabled={forgotPasswordLoading}
            loading={forgotPasswordLoading}
          >
            Send Reset Link
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ResetPasswordForm;
