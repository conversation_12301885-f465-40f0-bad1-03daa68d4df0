import authBackground from '@/assets/images/auth/auth-img.jpg';

import { useState } from 'react';
import ResetLinkSent from './components/ResetLinkSent';
import ResetPasswordForm from './components/ResetPasswordForm';
import FooterCopyright from '@/components/footer/FooterCopyright';
import Unity<PERSON>ogo from '@/components/unity-logo/UnityLogo';
import { useHideLogoOnZoom } from '@/hooks/useZoomLevel';

export const ForgotPassword = () => {
  const [isResetLinkSent, setIsResetLinkSent] = useState(false);
  const isHideLogo = useHideLogoOnZoom();

  return (
    <div className='flex relative flex-1'>
      {/* Logo */}
      {!isHideLogo && (
        <div className='absolute top-10 left-4 sm:left-10'>
          <UnityLogo />
        </div>
      )}

      {/* Left side - Form */}
      <div className='w-full sm:w-1/2 mt-20 sm:mt-0 flex flex-col justify-start sm:justify-center items-center p-4 sm:p-8 overflow-y-auto max-h-screen'>
        {isResetLinkSent ? (
          <ResetLinkSent />
        ) : (
          <ResetPasswordForm setIsResetLinkSent={setIsResetLinkSent} />
        )}
      </div>

      {/* Right side - Image */}
      <div className='hidden sm:block sm:w-1/2 h-screen'>
        <img src={authBackground} alt='Unity community' className='w-full h-full object-cover' />
      </div>
      {!isHideLogo && <FooterCopyright />}
    </div>
  );
};
