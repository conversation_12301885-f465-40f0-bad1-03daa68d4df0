import { useState } from 'react';
import PasswordCreated from './components/PasswordCreated';
import authBackground from '@/assets/images/auth/auth-img.jpg';
import { PasswordForm } from '../../../components/password-form/PasswordForm';
import { useSearchParams } from 'react-router-dom';
import FooterCopyright from '@/components/footer/FooterCopyright';
import Unity<PERSON>ogo from '@/components/unity-logo/UnityLogo';
import { useHideLogoOnZoom } from '@/hooks/useZoomLevel';

export const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const [isPasswordCreated, setIsPasswordCreated] = useState(false);
  const isHideLogo = useHideLogoOnZoom();

  const email = searchParams.get('email') || '';
  const otp = searchParams.get('otp') || '';

  return (
    <div className='flex relative flex-1'>
      {/* Logo */}
      {!isHideLogo && (
        <div className='absolute top-10 left-4 sm:left-10'>
          <UnityLogo />
        </div>
      )}
      {/* Left side - Form */}
      <div className='w-full sm:w-1/2 mt-20 sm:mt-0 flex flex-col justify-start sm:justify-center items-center p-4 sm:p-8 overflow-y-auto max-h-screen'>
        {isPasswordCreated ? (
          <PasswordCreated />
        ) : (
          <PasswordForm
            email={email}
            otp={otp}
            mode='reset'
            setIsPasswordCreated={setIsPasswordCreated}
          />
        )}
      </div>

      {/* Right side - Image */}
      <div className='hidden sm:block sm:w-1/2 h-screen'>
        <img src={authBackground} alt='Unity community' className='w-full h-full object-cover' />
      </div>
      {!isHideLogo && <FooterCopyright />}
    </div>
  );
};
