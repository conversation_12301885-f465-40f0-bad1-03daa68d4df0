import { Button } from '@/components/ui/Button';
import successIcon from '@/assets/images/success-icon.svg';
import { useNavigate } from 'react-router-dom';
import { AppRoutePaths } from '@/lib/constants';

const PasswordCreated = () => {
  const navigate = useNavigate();

  return (
    <div className='mt-20 sm:mt-0 w-full h-full flex flex-col justify-start sm:justify-center items-center flex-1 sm:max-w-[360px]'>
      <div className='gap-4 sm:gap-8 flex flex-col'>
        <div className='flex justify-start items-start '>
          <img src={successIcon} alt='success' className='w-12 h-12' />
        </div>
        <h1 className='text-2xl sm:text-4xl text-gray-900 font-semibold'>
          Your password has been successfully reset
        </h1>
        <p className='text-base'>You can now log in with your new password.</p>
        <Button className='w-full' onClick={() => navigate(AppRoutePaths.SIGN_IN)}>
          Go to login
        </Button>
      </div>
    </div>
  );
};

export default PasswordCreated;
