query me {
  me {
    id
    firstName
    lastName
    email
    role
  }
}

mutation register($input: RegisterInput!) {
  register(input: $input) {
    message
  }
}

mutation login($input: LoginInput!) {
  login(input: $input) {
    accessToken
    refreshToken
    accessTokenExpireTime
    refreshTokenExpireTime
  }
}

mutation refreshToken($input: RefreshTokenInput!) {
  refreshToken(input: $input) {
    accessToken
    refreshToken
    accessTokenExpireTime
    refreshTokenExpireTime
  }
}

mutation forgotPassword($input: ForgotPasswordInput!) {
  forgotPassword(input: $input) {
    message
  }
}

mutation resetPassword($input: ResetPasswordInput!) {
  resetPassword(input: $input) {
    message
  }
}

mutation CompleteSignup($input: CompleteSignupInput!) {
  completeSignup(input: $input) {
    accessToken
    refreshToken
    accessTokenExpireTime
    refreshTokenExpireTime
  }
}
