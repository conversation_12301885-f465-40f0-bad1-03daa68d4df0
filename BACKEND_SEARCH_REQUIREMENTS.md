# Backend Search Requirements for ClubRequests

## 📋 Current Issue

The frontend needs server-side search functionality for the ClubRequests feature in the Requests tab.

## 🎯 Required Changes

### 1. Update GraphQL Schema

**Add search field to ClubRequestFilterInput:**

```graphql
type ClubRequestFilterInput {
  category?: ClubCategoryEnum
  status?: ClubRequestStatus
  userId?: ID
  search?: String  # 👈 ADD THIS FIELD
}
```

### 2. Backend Implementation

The `search` field should search across these fields:

- `clubName` - Club name from request
- `clubDescription` - Club description
- `firstName` - User first name
- `lastName` - User last name
- `email` - User email
- `phone` - User phone number

**Recommended search logic:**

```sql
WHERE (
  LOWER(club_name) LIKE LOWER('%{search}%') OR
  LOWER(club_description) LIKE LOWER('%{search}%') OR
  LOWER(first_name) LIKE LOWER('%{search}%') OR
  LOWER(last_name) LIKE LOWER('%{search}%') OR
  LOWER(email) LIKE LOWER('%{search}%') OR
  phone LIKE '%{search}%'
)
```

### 3. API Behavior

**When search is provided:**

- Search across multiple fields (OR logic)
- Case-insensitive search
- Partial matches allowed
- Combined with other filters (AND logic)

**Example API call:**

```graphql
query ClubRequests(
  $paginationArgs: PaginationArgs
  $filter: ClubRequestFilterInput
) {
  clubRequests(
    paginationArgs: $paginationArgs
    filter: {
      category: CREATIVE
      status: PENDING
      search: "john"  # 👈 Will search "john" across all fields
    }
  ) {
    items { ... }
    total
  }
}
```

## 🚀 Frontend Ready

The frontend code is already prepared and will automatically start using server-side search once the backend implements the `search` field.

**Current frontend implementation:**

- ✅ UI components ready
- ✅ State management ready
- ✅ API integration ready (commented out)
- ✅ Fallback to client-side search (100 records)

## 🔄 Migration Plan

1. **Backend adds search field** to schema
2. **Frontend uncomments** search API usage
3. **Remove client-side search** logic
4. **Better performance** - true server-side search

## 📝 Notes

- Currently frontend loads 100 records for client-side search
- With server-side search, can reduce to normal pagination (10 records)
- Will improve performance significantly for large datasets
