/* craco.config.js */
// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require('path');

module.exports = {
  webpack: {
    alias: {
      '@': path.resolve(__dirname, 'src/'),
      '@/components': path.resolve(__dirname, 'src/components/*'),
      '@/pages': path.resolve(__dirname, 'src/pages/*'),
      '@/assets': path.resolve(__dirname, 'src/assets/*'),
      '@/hooks': path.resolve(__dirname, 'src/hooks/*'),
    },
  },
};
